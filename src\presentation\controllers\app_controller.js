// Importações das dependências
import { AuthUseCase } from '../../domain/usecases/auth_usecase.js';
import { ProductUseCase } from '../../domain/usecases/product_usecase.js';
import { AuthRepository } from '../../infrastructure/repositories/auth_repository.js';
import { ProductRepository } from '../../infrastructure/repositories/product_repository.js';
import { SupabaseService } from '../../infrastructure/services/supabase_service.js';
import { AuthController } from './auth_controller.js';
import { ProductController } from './product_controller.js';

/**
 * Controller principal da aplicação - Gerencia dependências e coordena outros controllers
 */
export class AppController {
  constructor(config = {}) {
    this.config = {
      supabaseUrl: config.supabaseUrl || 'https://your-project.supabase.co',
      supabaseAnonKey: config.supabaseAnonKey || 'your-anon-key',
      ...config
    };

    this.isInitialized = false;
    this.initializeDependencies();
  }

  /**
   * Inicializa todas as dependências da aplicação
   */
  initializeDependencies() {
    // Camada de Infraestrutura
    this.supabaseService = new SupabaseService(
      this.config.supabaseUrl,
      this.config.supabaseAnonKey
    );

    this.authRepository = new AuthRepository(this.supabaseService);
    this.productRepository = new ProductRepository(this.supabaseService);

    // Camada de Domínio
    this.authUseCase = new AuthUseCase(this.authRepository);
    this.productUseCase = new ProductUseCase(this.productRepository);

    // Camada de Apresentação
    this.authController = new AuthController(this.authUseCase);
    this.productController = new ProductController(this.productUseCase);

    this.setupControllerListeners();
  }

  /**
   * Configura listeners entre controllers
   */
  setupControllerListeners() {
    // Listener para mudanças de autenticação
    this.authController.addAuthStateListener((authState) => {
      if (authState.isAuthenticated) {
        // Usuário autenticado - inicializa produtos
        this.productController.initialize();
      } else {
        // Usuário desautenticado - limpa dados
        this.productController.products = [];
        this.productController.notifyListeners({
          type: 'products_cleared',
          data: { products: [], count: 0 }
        });
      }
    });
  }

  /**
   * Inicializa a aplicação
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Inicializando aplicação...');

      // Inicializa controllers
      await this.authController.initialize();

      // Se usuário está autenticado, inicializa produtos
      const isAuthenticated = await this.authController.isAuthenticated();
      if (isAuthenticated) {
        await this.productController.initialize();
      }

      this.isInitialized = true;
      console.log('Aplicação inicializada com sucesso');
    } catch (error) {
      console.error('Erro ao inicializar aplicação:', error);
      throw error;
    }
  }

  /**
   * Obtém controller de autenticação
   * @returns {AuthController}
   */
  getAuthController() {
    return this.authController;
  }

  /**
   * Obtém controller de produtos
   * @returns {ProductController}
   */
  getProductController() {
    return this.productController;
  }

  /**
   * Atualiza configuração do Supabase
   * @param {string} supabaseUrl 
   * @param {string} supabaseAnonKey 
   */
  updateSupabaseConfig(supabaseUrl, supabaseAnonKey) {
    this.config.supabaseUrl = supabaseUrl;
    this.config.supabaseAnonKey = supabaseAnonKey;

    // Reinicializa dependências com nova configuração
    this.initializeDependencies();
  }

  /**
   * Realiza cleanup da aplicação
   */
  cleanup() {
    try {
      // Para atualizações periódicas se existirem
      if (this.productController.updateInterval) {
        this.productController.stopPeriodicUpdate(this.productController.updateInterval);
      }

      // Limpa listeners
      this.authController.listeners = [];
      this.productController.listeners = [];

      console.log('Cleanup da aplicação realizado');
    } catch (error) {
      console.error('Erro no cleanup:', error);
    }
  }

  /**
   * Obtém status da aplicação
   * @returns {Object}
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      config: {
        supabaseUrl: this.config.supabaseUrl,
        hasAnonKey: !!this.config.supabaseAnonKey
      },
      controllers: {
        auth: !!this.authController,
        product: !!this.productController
      }
    };
  }

  /**
   * Método estático para criar instância com configuração padrão
   * @param {Object} config 
   * @returns {AppController}
   */
  static create(config = {}) {
    return new AppController(config);
  }
}