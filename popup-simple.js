document.addEventListener('DOMContentLoaded', async () => {
  const statusDiv = document.getElementById('status');
  const toggleBtn = document.getElementById('toggleBtn');
  const openSidebarBtn = document.getElementById('openSidebarBtn');

  // Carrega status inicial
  try {
    const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
    updateUI(response.enabled);
  } catch (error) {
    console.error('Erro ao carregar status:', error);
  }

  // Toggle automação
  toggleBtn.addEventListener('click', async () => {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'toggleAutomation' });
      updateUI(response.enabled);
    } catch (error) {
      console.error('Erro ao alternar automação:', error);
    }
  });

  // Abrir sidebar
  openSidebarBtn.addEventListener('click', async () => {
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await chrome.sidePanel.open({ tabId: tab.id });
      window.close();
    } catch (error) {
      console.error('Erro ao abrir sidebar:', error);
    }
  });

  function updateUI(enabled) {
    if (enabled) {
      statusDiv.textContent = 'Automação Ativa';
      statusDiv.className = 'status active';
      toggleBtn.textContent = 'Desativar Automação';
    } else {
      statusDiv.textContent = 'Automação Inativa';
      statusDiv.className = 'status inactive';
      toggleBtn.textContent = 'Ativar Automação';
    }
  }
});