class DateUtils {
  // Converte string de data para objeto Date
  static parseDate(dateString) {
    return new Date(dateString);
  }

  // Verifica se é hora de enviar o produto
  static isTimeToSend(scheduleDate) {
    const now = new Date();
    const scheduled = this.parseDate(scheduleDate);

    // Considera uma margem de 1 minuto para execução
    const timeDiff = Math.abs(now.getTime() - scheduled.getTime());
    return timeDiff <= 60000; // 60 segundos
  }

  // Formata data para exibição
  static formatDate(date, { timeZone = 'America/Bahia', ...overrides } = {}) {
    const parsedDate = new Date(date);

    if (Number.isNaN(parsedDate.getTime())) {
      return '';
    }

    const baseOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hourCycle: 'h23',
      timeZone
    };

    const formatter = new Intl.DateTimeFormat('pt-BR', { ...baseOptions, ...overrides, timeZone });
    return formatter.format(parsedDate);
  }
}

class SupabaseClient
