// Instância global do gerenciador de produtos (para compatibilidade)
let productsManager = null;

// Inicializa a view de produtos
document.addEventListener('DOMContentLoaded', () => {
  // Aguarda um pouco para garantir que todos os scripts foram carregados
  setTimeout(() => {
    if (typeof ProductsPage !== 'undefined') {
      const productsPageInstance = ProductsPage.createGlobalInstance();

      // Cria um wrapper para compatibilidade com o código existente
      productsManager = {
        // Métodos principais
        fetchAllScheduledProducts: () => productsPageInstance.loadProducts(),
        getScheduledProductsCount: () => productsPageInstance.getProductsCount(),
        startRealtimeMonitoring: () => productsPageInstance.startRealtimeMonitoring(),
        stopRealtimeMonitoring: () => productsPageInstance.stopRealtimeMonitoring(),

        // Propriedades
        get products() { return productsPageInstance.getAllProducts(); },

        // Métodos utilitários
        getOrderedProducts: () => productsPageInstance.getOrderedProducts(),
        getNextProducts: (limit) => productsPageInstance.getNextProducts(limit),
        notifyUpdate: () => productsPageInstance.notifyProductsUpdate()
      };

      console.log('✅ ProductsManager (compatibilidade) inicializado');
    } else {
      console.error('ProductsPage não está disponível');
    }
  }, 200);
});