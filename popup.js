// JavaScript do Popup para controle da extensão
document.addEventListener('DOMContentLoaded', async () => {
  const elements = {
    statusCard: document.getElementById('statusCard'),
    statusIndicator: document.getElementById('statusIndicator'),
    statusTitle: document.getElementById('statusTitle'),
    statusText: document.getElementById('statusText'),
    toggleBtn: document.getElementById('toggleBtn'),
    totalProducts: document.getElementById('totalProducts'),
    pendingProducts: document.getElementById('pendingProducts'),
    sentProducts: document.getElementById('sentProducts'),
    errorProducts: document.getElementById('errorProducts'),
    messageContainer: document.getElementById('messageContainer'),
    error: document.getElementById('error'),
    success: document.getElementById('success'),
    pendingCount: document.getElementById('pendingCount'),
    sentCount: document.getElementById('sentCount'),
    testConnectionBtn: document.getElementById('testConnectionBtn'),
    loading: document.getElementById('loading'),
    mainContent: document.getElementById('mainContent'),
    automationToggle: document.getElementById('automationToggle'),
    channelName: document.getElementById('channelName'),
    saveConfigBtn: document.getElementById('saveConfigBtn'),
    openWhatsAppBtn: document.getElementById('openWhatsAppBtn')
  };

  let currentConfig = {
    enabled: false,
    channelName: 'Promobell {Dev}',
    environment: 'dev',
    isRunning: false
  };

  // Inicialização
  await init();

  async function init() {
    try {
      if (elements.loading) elements.loading.style.display = 'block';
      if (elements.mainContent) elements.mainContent.style.display = 'none';

      // Aguarda mais tempo para garantir que o background script foi inicializado
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Carrega status atual
      await loadStatus();

      // Carrega ambiente atual
      await loadEnvironment();

      // Configura event listeners
      setupEventListeners();

      // Carrega estatísticas (sem bloquear a UI)
      loadStats().catch(console.error);

      if (elements.loading) elements.loading.style.display = 'none';
      if (elements.mainContent) elements.mainContent.style.display = 'block';

    } catch (error) {
      console.error('Erro ao inicializar:', error);
      showError('Erro ao inicializar: ' + error.message);
      if (elements.loading) elements.loading.style.display = 'none';
      if (elements.mainContent) elements.mainContent.style.display = 'block';
    }
  }

  async function loadStatus() {
    console.log('🔄 Popup: Carregando status...');

    try {
      // Verifica se o background script está disponível
      if (!chrome.runtime?.id) {
        throw new Error('Background script não está disponível');
      }

      const response = await chrome.runtime.sendMessage({ action: 'getStatus' });
      console.log('📨 Popup: Resposta recebida do background:', response);

      // Verifica se a resposta é válida e tem as propriedades esperadas
      if (response && typeof response === 'object') {
        currentConfig.enabled = Boolean(response.enabled);
        currentConfig.channelName = response.channelName || 'Promobell {Dev}';
        currentConfig.isRunning = Boolean(response.isRunning);
        console.log('✅ Popup: Configuração atualizada:', currentConfig);
      } else {
        console.log('⚠️ Popup: Resposta inválida ou vazia, usando valores padrão');
        // Valores padrão se não conseguir comunicar
        currentConfig.enabled = false;
        currentConfig.channelName = 'Promobell {Dev}';
        currentConfig.isRunning = false;
      }

      updateUI();

      // Sempre mostra o conteúdo, mesmo com erro
      elements.loading.style.display = 'none';
      elements.mainContent.style.display = 'block';

    } catch (error) {
      console.error('❌ Popup: Erro ao carregar status:', error);

      // Define valores padrão em caso de erro
      currentConfig.enabled = false;
      currentConfig.channelName = 'Promobell {Dev}';
      currentConfig.isRunning = false;

      updateUI();

      // Sempre mostra o conteúdo, mesmo com erro
      elements.loading.style.display = 'none';
      elements.mainContent.style.display = 'block';

      showError('Erro na conexão: ' + error.message);
    }
  }

  async function loadEnvironment() {
    try {
      const result = await chrome.storage.local.get(['environment']);
      currentConfig.environment = result.environment || 'dev';
    } catch (error) {
      console.error('Erro ao carregar ambiente:', error);
    }
  }

  async function loadStats() {
    try {
      const response = await chrome.runtime.sendMessage({ action: 'testConnection' });

      if (response && response.success) {
        // Atualiza elementos do popup (se existirem)
        if (elements.pendingCount) {
          elements.pendingCount.textContent = response.count || 0;
        }
        if (elements.sentCount) {
          elements.sentCount.textContent = '-';
        }

        // Atualiza elementos do sidebar (se existirem)
        if (elements.pendingProducts) {
          elements.pendingProducts.textContent = response.count || 0;
        }
        if (elements.sentProducts) {
          elements.sentProducts.textContent = '-';
        }
        
        console.log(`📊 Contador atualizado: ${response.count} produtos agendados`);
      } else {
        console.error('❌ Erro ao carregar estatísticas:', response?.error);
        // Zera contadores em caso de erro
        if (elements.pendingCount) elements.pendingCount.textContent = '0';
        if (elements.sentCount) elements.sentCount.textContent = '0';
        if (elements.pendingProducts) elements.pendingProducts.textContent = '0';
        if (elements.sentProducts) elements.sentProducts.textContent = '0';
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
      // Zera contadores em caso de erro
      if (elements.pendingCount) elements.pendingCount.textContent = '0';
      if (elements.sentCount) elements.sentCount.textContent = '0';
      if (elements.pendingProducts) elements.pendingProducts.textContent = '0';
      if (elements.sentProducts) elements.sentProducts.textContent = '0';
    }
  }

  function updateUI() {
    // Atualiza status card
    if (currentConfig.enabled && currentConfig.isRunning) {
      if (elements.statusCard) elements.statusCard.classList.remove('inactive');
      if (elements.statusIndicator) elements.statusIndicator.classList.remove('inactive');
      if (elements.statusTitle) elements.statusTitle.textContent = 'Automação Ativa';
      if (elements.statusText) elements.statusText.textContent = 'Monitorando produtos agendados';
    } else if (currentConfig.enabled && !currentConfig.isRunning) {
      if (elements.statusCard) elements.statusCard.classList.remove('inactive');
      if (elements.statusIndicator) elements.statusIndicator.classList.remove('inactive');
      if (elements.statusTitle) elements.statusTitle.textContent = 'Automação Habilitada';
      if (elements.statusText) elements.statusText.textContent = 'Aguardando inicialização';
    } else {
      if (elements.statusCard) elements.statusCard.classList.add('inactive');
      if (elements.statusIndicator) elements.statusIndicator.classList.add('inactive');
      if (elements.statusTitle) elements.statusTitle.textContent = 'Automação Inativa';
      if (elements.statusText) elements.statusText.textContent = 'Clique no toggle para ativar';
    }

    // Atualiza campos
    if (elements.channelName) elements.channelName.value = currentConfig.channelName;
    if (elements.automationToggle) elements.automationToggle.checked = currentConfig.enabled;
  }

  function setupEventListeners() {
    // Toggle de automação
    if (elements.automationToggle) {
      elements.automationToggle.addEventListener('change', async (e) => {
        console.log('🔄 Popup: Toggle de automação clicado, estado:', e.target.checked);

        const newState = e.target.checked;

        try {
          // Verifica se o background script está disponível
          if (!chrome.runtime?.id) {
            throw new Error('Background script não está disponível');
          }

          console.log('📤 Popup: Enviando mensagem toggleAutomation...');
          const response = await chrome.runtime.sendMessage({ action: 'toggleAutomation' });
          console.log('📨 Popup: Resposta do toggleAutomation:', response);

          // Verifica se a resposta é válida
          if (response && typeof response === 'object' && response.hasOwnProperty('enabled')) {
            console.log('✅ Popup: Estado confirmado pelo background:', response.enabled);

            // Força a atualização do toggle para refletir o estado real
            const confirmedState = Boolean(response.enabled);
            if (elements.automationToggle) elements.automationToggle.checked = confirmedState;
            currentConfig.enabled = confirmedState;

            if (response.success) {
              console.log('✅ Popup: Automação alterada com sucesso');
              showSuccess(confirmedState ? 'Automação ativada!' : 'Automação desativada!');
            } else {
              console.log('⚠️ Popup: Operação não foi bem-sucedida');
              showError('Erro ao alterar automação');
            }
          } else {
            console.error('❌ Popup: Resposta inválida do background:', response);
            // Reverte o estado do toggle
            if (elements.automationToggle) elements.automationToggle.checked = !newState;
            showError('Resposta inválida do servidor');
          }

        } catch (error) {
          console.error('❌ Popup: Erro ao alterar automação:', error);
          // Reverte o estado do toggle em caso de erro
          if (elements.automationToggle) elements.automationToggle.checked = !newState;
          showError('Erro na comunicação: ' + error.message);
        }
      });
    }

    // Botão de teste de conexão
    if (elements.testConnectionBtn) {
      elements.testConnectionBtn.addEventListener('click', async () => {
        if (elements.testConnectionBtn) {
          elements.testConnectionBtn.textContent = 'Testando...';
          elements.testConnectionBtn.disabled = true;
        }

        try {
          const response = await chrome.runtime.sendMessage({ action: 'testConnection' });

          if (response && response.success) {
            showSuccess(`Conexão OK! ${response.count} produtos encontrados`);
            await loadStats();
          } else {
            showError('Erro na conexão: ' + (response?.error || 'Desconhecido'));
          }
        } catch (error) {
          showError('Erro ao testar conexão: ' + error.message);
        } finally {
          if (elements.testConnectionBtn) {
            elements.testConnectionBtn.textContent = 'Testar Conexão';
            elements.testConnectionBtn.disabled = false;
          }
        }
      });
    }

    // Botão para abrir WhatsApp Web
    if (elements.openWhatsAppBtn) {
      elements.openWhatsAppBtn.addEventListener('click', () => {
        chrome.tabs.create({ url: 'https://web.whatsapp.com' });
      });
    }

    // Botão salvar configurações
    if (elements.saveConfigBtn) {
      elements.saveConfigBtn.addEventListener('click', async () => {
        try {
          const newChannelName = elements.channelName ? elements.channelName.value.trim() : '';

          if (!newChannelName) {
            showError('Nome do canal não pode estar vazio');
            return;
          }

          const response = await chrome.runtime.sendMessage({
            action: 'updateChannelName',
            channelName: newChannelName
          });

          if (response && response.success) {
            currentConfig.channelName = newChannelName;
            showSuccess('Configurações salvas com sucesso');
          } else {
            showError('Erro ao salvar configurações');
          }
        } catch (error) {
          showError('Erro ao salvar: ' + error.message);
        }
      });
    }

    // Auto-save no campo do canal
    if (elements.channelName) {
      elements.channelName.addEventListener('blur', async () => {
        const newChannelName = elements.channelName.value.trim();
        if (newChannelName && newChannelName !== currentConfig.channelName) {
          try {
            const response = await chrome.runtime.sendMessage({
              action: 'updateChannelName',
              channelName: newChannelName
            });

            if (response && response.success) {
              currentConfig.channelName = newChannelName;
            }
          } catch (error) {
            console.error('Erro ao salvar canal automaticamente:', error);
          }
        }
      });
    }
  }

  function showError(message) {
    if (elements.error) {
      elements.error.textContent = message;
      elements.error.style.display = 'block';
    }
    if (elements.success) {
      elements.success.style.display = 'none';
    }

    setTimeout(() => {
      if (elements.error) {
        elements.error.style.display = 'none';
      }
    }, 5000);
  }

  function showSuccess(message) {
    if (elements.success) {
      elements.success.textContent = message;
      elements.success.style.display = 'block';
    }
    if (elements.error) {
      elements.error.style.display = 'none';
    }

    setTimeout(() => {
      if (elements.success) {
        elements.success.style.display = 'none';
      }
    }, 3000);
  }

  // Atualiza estatísticas periodicamente
  setInterval(loadStats, 30000); // A cada 30 segundos
});