// Configuração de ambiente para a extensão
class Config {
  constructor() {
    this.envLoader = null;
    this.isInitialized = false;
  }

  // Inicializa a configuração carregando as variáveis de ambiente
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Importa o env-loader se não estiver disponível
      if (typeof envLoader === 'undefined') {
        await this.loadEnvLoader();
      }

      this.envLoader = envLoader;
      await this.envLoader.initialize();
      this.isInitialized = true;
      
      console.log('✅ Config: Inicializado com sucesso');
    } catch (error) {
      console.error('❌ Config: Erro ao inicializar:', error);
      throw error;
    }
  }

  // Carrega o env-loader dinamicamente
  async loadEnvLoader() {
    try {
      // Verifica se estamos em um contexto com DOM
      if (typeof document !== 'undefined') {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = chrome.runtime.getURL('env-loader.js');
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      } else {
        // Em service workers, importa diretamente
        await import(chrome.runtime.getURL('env-loader.js'));
        return Promise.resolve();
      }
    } catch (error) {
      console.error('❌ Config: Erro ao carregar env-loader:', error);
      throw error;
    }
  }

  // Obtém a configuração atual
  async get() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log('🔄 Config: Obtendo configuração atual para ambiente:', this.envLoader.getCurrentEnvironment());
    
    const config = {
      SUPABASE_URL: this.envLoader.get('SUPABASE_URL'),
      SUPABASE_ANON_KEY: this.envLoader.get('SUPABASE_ANON_KEY'),
      SUPABASE_SERVICE_ROLE_KEY: this.envLoader.get('SUPABASE_SERVICE_ROLE_KEY'),
      OPENAI_API_KEY: this.envLoader.get('OPENAI_API_KEY'),
      GOOGLE_AI_STUDIO_KEY: this.envLoader.get('GOOGLE_AI_STUDIO_KEY'),
      SCRAPE_OPS_API_KEY: this.envLoader.get('SCRAPE_OPS_API_KEY'),
      DO_REGION: this.envLoader.get('DO_REGION'),
      DO_BUCKET_NAME: this.envLoader.get('DO_BUCKET_NAME'),
      DO_ACCESS_KEY: this.envLoader.get('DO_ACCESS_KEY'),
      DO_ACCESS_TOKEN: this.envLoader.get('DO_ACCESS_TOKEN'),
      DO_SECRET_KEY: this.envLoader.get('DO_SECRET_KEY'),
      DO_SPACES_ENDPOINT: this.envLoader.get('DO_SPACES_ENDPOINT')
    };
    
    console.log('✅ Config: Configuração obtida:', { 
      ...config, 
      SUPABASE_ANON_KEY: '***', 
      SUPABASE_SERVICE_ROLE_KEY: '***',
      OPENAI_API_KEY: '***',
      DO_SECRET_KEY: '***'
    });
    
    return config;
  }

  // Cria cliente Supabase usando o SDK oficial
  async createSupabaseClient() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const config = await this.get();
    
    // Verifica se o SDK do Supabase está disponível
    if (typeof supabase === 'undefined') {
      throw new Error('SDK do Supabase não está disponível');
    }

    const client = supabase.createClient(config.SUPABASE_URL, config.SUPABASE_ANON_KEY, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false,
        // Configurações específicas para extensões Chrome
        storage: {
          getItem: async (key) => {
            try {
              const result = await chrome.storage.local.get([key]);
              return result[key] || null;
            } catch (error) {
              console.error('Erro ao obter item do storage:', error);
              return null;
            }
          },
          setItem: async (key, value) => {
            try {
              await chrome.storage.local.set({ [key]: value });
            } catch (error) {
              console.error('Erro ao salvar item no storage:', error);
            }
          },
          removeItem: async (key) => {
            try {
              await chrome.storage.local.remove([key]);
            } catch (error) {
              console.error('Erro ao remover item do storage:', error);
            }
          }
        },
        // Configurações de debug para desenvolvimento
        debug: config.NODE_ENV === 'development'
      }
    });

    console.log('✅ Config: Cliente Supabase criado com sucesso');
    return client;
  }

  // Alterna entre dev e prod
  async setEnvironment(env) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log('🔄 Config: Alterando ambiente para:', env);

    if (env === 'dev' || env === 'prod') {
      await this.envLoader.setEnvironment(env);
      console.log(`✅ Config: Ambiente alterado para: ${env}`);

      // Verifica se realmente foi salvo
      const verification = await chrome.storage.local.get(['environment']);
      console.log('🔍 Config: Verificação após salvar ambiente:', verification);
    } else {
      console.error('❌ Config: Ambiente inválido:', env);
    }
  }

  // Carrega o ambiente salvo
  async loadEnvironment() {
    if (!this.isInitialized) {
      await this.initialize();
    }

    console.log('🔄 Config: Carregando ambiente salvo...');
    await this.envLoader.loadSavedEnvironment();
    console.log('✅ Config: Ambiente carregado:', this.envLoader.getCurrentEnvironment());
  }

  // Obtém URL do Supabase
  async getSupabaseUrl() {
    const config = await this.get();
    const url = config.SUPABASE_URL;
    
    // Validação e sanitização
    if (typeof url !== 'string') {
      console.warn('⚠️ Config: SUPABASE_URL não é uma string:', typeof url, url);
      return '';
    }
    
    return url.trim();
  }

  // Obtém chave anônima do Supabase
  async getSupabaseAnonKey() {
    const config = await this.get();
    const key = config.SUPABASE_ANON_KEY;
    
    // Validação e sanitização
    if (typeof key !== 'string') {
      console.warn('⚠️ Config: SUPABASE_ANON_KEY não é uma string:', typeof key, key);
      return '';
    }
    
    return key.trim();
  }

  // Obtém chave de serviço do Supabase
  async getSupabaseServiceRoleKey() {
    const config = await this.get();
    const key = config.SUPABASE_SERVICE_ROLE_KEY;
    
    // Validação e sanitização
    if (typeof key !== 'string') {
      console.warn('⚠️ Config: SUPABASE_SERVICE_ROLE_KEY não é uma string:', typeof key, key);
      return '';
    }
    
    return key.trim();
  }

  // Obtém ambiente atual
  getCurrentEnvironment() {
    if (!this.isInitialized || !this.envLoader) {
      return 'dev'; // fallback
    }
    return this.envLoader.getCurrentEnvironment();
  }

  // Verifica se está inicializado
  isConfigInitialized() {
    return this.isInitialized;
  }
}

// Instância global da configuração
const config = new Config();

// Para uso em módulos ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { Config, config };
}

// Para uso global (Service Workers e páginas)
if (typeof window !== 'undefined') {
  window.Config = Config;
  window.config = config;
} else if (typeof self !== 'undefined') {
  // Para Service Workers
  self.Config = Config;
  self.config = config;
}