// Background Service Worker para automação do WhatsApp
// Monitora produtos agendados e coordena o envio automático

// Importa os scripts necessários usando importScripts (compatível com Service Workers)
try {
  importScripts('supabase.js');    // Primeiro o SDK do Supabase
  importScripts('env-loader.js');  // Depois env-loader.js
  importScripts('config.js');      // Depois config.js (depende do env-loader)
  importScripts('utils.js');       // Depois utils.js (pode usar config)
  importScripts('auth.js');        // Depois auth.js (usa config)
  importScripts('app_controller.js'); // Importa o AppController (versão compatível)
  console.log('✅ Background: Scripts importados com sucesso');
} catch (error) {
  console.error('❌ Background: Erro ao importar scripts:', error);
}

// Variáveis globais para os serviços
let authServiceInstance = null;
let configInstance = null;
let appControllerInstance = null;

// Função para inicializar os serviços
async function initializeServices() {
  try {
    console.log('🔄 Background: Iniciando serviços...');
    
    // Inicializa os serviços
    configInstance = new Config();
    await configInstance.initialize();
    console.log('✅ Background: Config inicializado');
    
    authServiceInstance = new AuthService();
    await authServiceInstance.initialize();
    console.log('✅ Background: AuthService inicializado');
    
    // Inicializa o AppController real
    if (typeof AppController !== 'undefined') {
      appControllerInstance = new AppController(configInstance);
      
      // Passa a instância do authService para o AppController
      if (authServiceInstance) {
        // Disponibiliza authServiceInstance globalmente para o AppController
        if (typeof self !== 'undefined') {
          self.authServiceInstance = authServiceInstance;
        }
      }
      
      await appControllerInstance.initialize();
      console.log('✅ Background: AppController inicializado');
    } else {
      console.warn('⚠️ Background: AppController não disponível');
    }
    
    console.log('✅ Background: Serviços inicializados com sucesso');
  } catch (error) {
    console.error('❌ Background: Erro ao inicializar serviços:', error);
  }
}

// Executado quando a extensão é instalada ou atualizada
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Promobell WhatsApp Automation instalada');
  
  // Inicializa os serviços
  await initializeServices();
  
  // Permite que o painel lateral seja aberto ao clicar no ícone da extensão
  if (chrome.sidePanel && chrome.sidePanel.setPanelBehavior) {
    chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch((err) => {
      console.warn('Falha ao definir comportamento do painel lateral:', err);
    });
  }
});

if (chrome.commands) {
  chrome.commands.onCommand.addListener(async (command) => {
    if (command === 'toggle-side-panel') {
      try {
        // Obtém a janela atual do navegador
        const [currentWindow] = await chrome.windows.getAll({ populate: false, windowTypes: ['normal'] });
        if (!currentWindow || !chrome.sidePanel) return;
        // Verifica se o painel está aberto
        const panelInfo = await chrome.sidePanel.get({ windowId: currentWindow.id });
        if (panelInfo && panelInfo.open) {
          // Fecha o painel se estiver aberto (Chrome 116+)
          if (chrome.sidePanel.close) {
            await chrome.sidePanel.close({ windowId: currentWindow.id });
          }
        } else {
          // Abre o painel lateral para a janela atual
          await chrome.sidePanel.open({ windowId: currentWindow.id });
        }
      } catch (err) {
        console.error('Erro ao alternar painel lateral:', err);
      }
    }
  });
}

// Importa scripts necessários para Service Worker
// Removido - usando imports dinâmicos em initializeServices()

// Verifica se as classes foram carregadas corretamente
// Removido - verificação será feita após imports dinâmicos

class WhatsAppAutomation {
  constructor() {
    this.supabaseClient = null;
    this.authServiceInstance = null;
    this.checkInterval = 60000; // 1 minuto
    this.isMonitoring = false;
    this.realtimeEnabled = true; // Flag para controlar realtime
  }

  async init() {
    try {
      // Aguarda inicialização dos serviços se necessário
      if (!configInstance || !authServiceInstance) {
        await initializeServices();
      }

      // Usa o authServiceInstance global
      this.authServiceInstance = authServiceInstance;
      
      // Disponibiliza AuthService no contexto global do service worker
      if (typeof self !== 'undefined') {
        self.authService = this.authServiceInstance;
      }

      // Verifica se SupabaseClient está disponível antes de usar
      if (typeof SupabaseClient !== 'undefined') {
        const [supabaseUrl, supabaseAnonKey, supabaseServiceRoleKey] = await Promise.all([
          configInstance.getSupabaseUrl(),
          configInstance.getSupabaseAnonKey(),
          configInstance.getSupabaseServiceRoleKey()
        ]);

        this.supabaseClient = new SupabaseClient({
          url: supabaseUrl,
          anonKey: supabaseServiceRoleKey || supabaseAnonKey,
          serviceRoleKey: supabaseServiceRoleKey || null,
          config: configInstance
        });

        await this.supabaseClient.init();
      } else {
        console.warn('⚠️ SupabaseClient não está disponível');
      }

      // Carrega configurações do storage local
      const settings = await chrome.storage.local.get(['checkInterval']);
      if (settings.checkInterval) {
        this.checkInterval = settings.checkInterval * 1000; // Converte para ms
      }

      // Inicia escuta em tempo real se habilitada
      if (this.realtimeEnabled) {
        await this.startRealtimeMonitoring();
      }

      console.log('WhatsApp Automation inicializado');
    } catch (error) {
      console.error('Erro ao inicializar:', error);
    }
  }

  // Inicia monitoramento em tempo real
  async startRealtimeMonitoring() {
    if (!this.supabaseClient) return;

    try {
      console.log('🚀 Iniciando monitoramento realtime...');

      // Usar o novo sistema realtime
      await this.supabaseClient.startRealtimeListener((payload) => {
        console.log('📡 Evento realtime recebido:', payload);

        // Processar diferentes tipos de eventos
        switch (payload.eventType) {
          case 'SCHEDULED_READY':
            // Produto pronto para ser enviado
            console.log('⏰ Produto pronto para envio:', payload.product);
            this.handleScheduledProduct(payload.product);
            break;
          case 'INSERT':
            console.log('➕ Novo produto agendado:', payload.new);
            this.handleNewScheduledProduct(payload.new);
            break;
          case 'UPDATE':
            console.log('🔄 Produto agendado atualizado:', payload.new);
            this.handleUpdatedScheduledProduct(payload.old, payload.new);
            break;
          case 'DELETE':
            console.log('🗑️ Produto agendado removido:', payload.old);
            break;
        }
      });

    } catch (error) {
      console.error('❌ Erro ao iniciar monitoramento realtime:', error);
      // Fallback para monitoramento por polling se realtime falhar
      console.log('🔄 Usando fallback para monitoramento por polling...');
      this.realtimeEnabled = false;
    }
  }

  // Processa produto que está pronto para ser enviado
  async handleScheduledProduct(product) {
    try {
      console.log(`⏰ Produto pronto para envio: ${product.titulo}`);

      // Verifica se a automação está ativada
      const settings = await chrome.storage.local.get(['automationEnabled']);
      if (!settings.automationEnabled) {
        console.log('Automação desativada');
        return;
      }

      await this.processProduct(product);
    } catch (error) {
      console.error('Erro ao processar produto agendado:', error);
    }
  }

  // Manipula novo produto agendado
  async handleNewScheduledProduct(product) {
    try {
      // Verifica se é para enviar imediatamente
      if (DateUtils.isTimeToSend(product.date_schedule)) {
        console.log('⚡ Produto deve ser enviado imediatamente');
        await this.processProduct(product);
      } else {
        console.log('⏰ Produto agendado para:', product.date_schedule);
      }
    } catch (error) {
      console.error('Erro ao processar novo produto:', error);
    }
  }

  // Manipula produto agendado atualizado
  async handleUpdatedScheduledProduct(oldProduct, newProduct) {
    try {
      // Verifica se houve mudança no agendamento
      if (oldProduct.date_schedule !== newProduct.date_schedule) {
        console.log('📅 Agendamento alterado de', oldProduct.date_schedule, 'para', newProduct.date_schedule);
      }

      // Verifica se foi acionado disparo manual
      if (!oldProduct.enviado_whatsapp && newProduct.enviado_whatsapp) {
        console.log('🚀 Disparo manual acionado para produto:', newProduct.titulo);
        await this.processProduct(newProduct);
      }

      // Verifica se é hora de enviar após atualização
      if (DateUtils.isTimeToSend(newProduct.date_schedule) && !newProduct.enviado_whatsapp) {
        console.log('⚡ Produto atualizado deve ser enviado agora');
        await this.processProduct(newProduct);
      }
    } catch (error) {
      console.error('Erro ao processar produto atualizado:', error);
    }
  }

  async startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    console.log('🔄 Iniciando monitoramento de produtos agendados...');

    // Se realtime está habilitado, usa apenas ele, senão usa polling
    if (this.realtimeEnabled) {
      console.log('📡 Usando monitoramento realtime');
      // Realtime já foi iniciado no init(), apenas mantém o estado
    } else {
      console.log('⏰ Usando monitoramento por polling');
      // Cria alarme para verificação periódica como fallback
      await chrome.alarms.create('checkScheduledProducts', {
        delayInMinutes: 1,
        periodInMinutes: this.checkInterval / 60000
      });
    }
  }

  async stopMonitoring() {
    this.isMonitoring = false;

    // Para alarmes se estiverem ativos
    await chrome.alarms.clear('checkScheduledProducts');

    // Para o listener realtime se estiver ativo
    if (this.supabaseClient && this.realtimeEnabled) {
      this.supabaseClient.stopRealtimeListener();
    }

    console.log('🛑 Monitoramento parado');
  }

  async openSidePanel(tabId) {
    try {
      // Usar windowId em vez de tabId para evitar erro de gesto do usuário
      const tab = await chrome.tabs.get(tabId);
      await chrome.sidePanel.open({ windowId: tab.windowId });
    } catch (error) {
      console.error('Erro ao abrir sidebar:', error);
    }
  }

  // Verifica produtos agendados (método legado - agora usa realtime)
  async checkScheduledProducts() {
    console.log('⚠️ Método checkScheduledProducts é legado - usando sistema realtime');
    // Este método não é mais necessário pois o sistema realtime
    // automaticamente detecta quando produtos estão prontos para envio
    return;
  }

  // Processa um produto individual
  async processProduct(product) {
    try {
      console.log(`Processando produto: ${product.titulo}`);

      // Verifica se é realmente hora de enviar
      if (!DateUtils.isTimeToSend(product.date_schedule)) {
        console.log('Ainda não é hora de enviar este produto');
        return;
      }

      // Garante que o WhatsApp Web está aberto
      await this.ensureWhatsAppTab();

      if (!this.whatsappTabId) {
        console.error('Não foi possível abrir o WhatsApp Web');
        return;
      }

      // Verifica se está no canal correto antes de enviar
      const channelCheckResult = await chrome.tabs.sendMessage(this.whatsappTabId, {
        action: 'checkChannel',
        channelName: this.channelName
      });

      if (!channelCheckResult || !channelCheckResult.success) {
        console.log('❌ Não está no canal correto ou canal não encontrado');

        // Tenta navegar para o canal correto
        const navigationResult = await chrome.tabs.sendMessage(this.whatsappTabId, {
          action: 'navigateToChannel',
          channelName: this.channelName
        });

        if (!navigationResult || !navigationResult.success) {
          console.error('Não foi possível navegar para o canal:', this.channelName);
          return;
        }

        // Aguarda um pouco para a navegação completar
        await new Promise(resolve => setTimeout(resolve, 2000));
      }

      // Gera a mensagem
      const message = MessageFormatter.generateWhatsAppMessage(product);

      // Envia mensagem via content script
      const result = await chrome.tabs.sendMessage(this.whatsappTabId, {
        action: 'sendMessage',
        channelName: this.channelName,
        message: message,
        imageUrl: product.url_imagem,
        productId: product.id
      });

      if (result && result.success) {
        // Só marca como enviado se o envio foi realmente bem-sucedido
        const markResult = await this.supabaseClient.markProductAsSentRealtime(product.id);

        if (markResult) {
          console.log(`✅ Produto ${product.id} enviado e marcado como enviado com sucesso`);

          // Notifica o usuário apenas se tudo deu certo
          chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/Promobell.png',
            title: 'Produto Enviado',
            message: `${product.titulo} foi enviado para o canal ${this.channelName}`
          });
        } else {
          console.error('⚠️ Produto enviado mas falha ao marcar no banco de dados');
        }
      } else {
        console.error('❌ Falha ao enviar produto:', result?.error);
        // NÃO marca como enviado se houve falha no envio
      }

    } catch (error) {
      console.error('Erro ao processar produto:', error);
    }
  }

  // Garante que uma aba do WhatsApp Web está aberta
  async ensureWhatsAppTab() {
    try {
      // Verifica se já existe uma aba do WhatsApp Web
      const tabs = await chrome.tabs.query({ url: 'https://web.whatsapp.com/*' });

      if (tabs.length > 0) {
        this.whatsappTabId = tabs[0].id;
        // Ativa a aba se não estiver ativa
        await chrome.tabs.update(this.whatsappTabId, { active: false }); // Mantém em background
        return;
      }

      // Se não existe, cria uma nova aba em background
      const tab = await chrome.tabs.create({
        url: 'https://web.whatsapp.com',
        active: false // Abre em background
      });

      this.whatsappTabId = tab.id;

      // Aguarda a aba carregar
      await new Promise((resolve) => {
        const listener = (tabId, changeInfo) => {
          if (tabId === this.whatsappTabId && changeInfo.status === 'complete') {
            chrome.tabs.onUpdated.removeListener(listener);
            resolve();
          }
        };
        chrome.tabs.onUpdated.addListener(listener);
      });

      console.log('WhatsApp Web aberto em background');

    } catch (error) {
      console.error('Erro ao abrir WhatsApp Web:', error);
      this.whatsappTabId = null;
    }
  }

  // Alterna estado da automação
  async toggleAutomation() {
    this.automationEnabled = !this.automationEnabled;
    await chrome.storage.local.set({ automationEnabled: this.automationEnabled });

    if (this.automationEnabled) {
      await this.startMonitoring();
    } else {
      this.stopMonitoring();
    }

    return this.automationEnabled;
  }

  // Atualiza nome do canal
  async updateChannelName(newName) {
    this.channelName = newName;
    await chrome.storage.local.set({ channelName: newName });
  }
}

// Instância global
let automation = null;

// Função para inicializar a automação
async function initializeAutomation() {
  try {
    if (!automation) {
      automation = new WhatsAppAutomation();
      await automation.init();
    }
  } catch (error) {
    console.error('❌ Background: Erro ao inicializar automação:', error);
  }
}

// Inicializa automaticamente quando o script carrega (com delay)
setTimeout(async () => {
  await initializeAutomation();
}, 2000); // Aumentado para 2 segundos para dar tempo dos imports

// Listener para quando a extensão é instalada ou atualizada
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Extensão instalada/atualizada');
  await initializeAutomation();
});

// Listener para quando o service worker inicia
chrome.runtime.onStartup.addListener(async () => {
  console.log('Service worker iniciado');
  await initializeAutomation();
});

// Listener para clique no ícone da extensão (abre sidebar)
if (chrome.action && chrome.action.onClicked) {
  chrome.action.onClicked.addListener(async (tab) => {
    if (automation) {
      await automation.openSidePanel(tab.id);
    }
  });
} else {
  console.error('chrome.action.onClicked não está disponível');
}

// Listener para alarmes
chrome.alarms.onAlarm.addListener(async (alarm) => {
  if (alarm.name === 'checkScheduledProducts' && automation) {
    await automation.checkScheduledProducts();
  }
});

// Listener para mensagens do popup/sidebar
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('🔄 Background recebeu mensagem:', message.action, message);

  // Função assíncrona para processar a mensagem
  (async () => {
    try {
      switch (message.action) {
        case 'getStatus':
          console.log('📊 Obtendo status da automação...');
          const settings = await chrome.storage.local.get(['automationEnabled', 'channelName']);
          console.log('💾 Configurações carregadas do storage:', settings);

          const response = {
            enabled: Boolean(settings.automationEnabled),
            channelName: settings.channelName || 'Promobell {Dev}',
            isRunning: automation ? automation.isMonitoring : false
          };
          console.log('✅ Status retornado:', response);
          sendResponse(response);
          break;

        case 'toggleAutomation':
          try {
            console.log('🔄 Alternando estado da automação...');
            const currentSettings = await chrome.storage.local.get(['automationEnabled']);
            console.log('📊 Estado atual:', currentSettings);

            const newState = !currentSettings.automationEnabled;
            console.log('🎯 Novo estado será:', newState);

            await chrome.storage.local.set({ automationEnabled: newState });
            console.log('💾 Estado salvo no storage');

            if (newState) {
              console.log('▶️ Iniciando monitoramento...');
              if (automation) {
                await automation.startMonitoring();
              }
            } else {
              console.log('⏹️ Parando monitoramento...');
              if (automation) {
                await automation.stopMonitoring();
              }
            }

            // Verifica se realmente foi salvo
            const verification = await chrome.storage.local.get(['automationEnabled']);
            console.log('✅ Verificação do storage após salvar:', verification);

            console.log('✅ Automação alterada para:', newState);
            sendResponse({ enabled: Boolean(newState), success: true });
          } catch (error) {
            console.error('❌ Erro ao alterar automação:', error);
            sendResponse({ enabled: false, success: false, error: error.message });
          }
          break;


        case 'setAutomationState':
          try {
            const desiredState = Boolean(message.enabled);
            console.log('🔄 Definindo estado da automação para:', desiredState);

            await chrome.storage.local.set({ automationEnabled: desiredState });

            if (automation) {
              if (desiredState) {
                await automation.startMonitoring();
              } else {
                await automation.stopMonitoring();
              }
            }

            sendResponse({ success: true, enabled: desiredState });
          } catch (error) {
            console.error('❌ Erro ao definir estado da automação:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'startMonitoring':
          if (automation) {
            await automation.startMonitoring();
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'Automation não inicializada' });
          }
          break;

        case 'stopMonitoring':
          if (automation) {
            await automation.stopMonitoring();
            sendResponse({ success: true });
          } else {
            sendResponse({ success: false, error: 'Automation não inicializada' });
          }
          break;

        case 'updateChannelName':
          try {
            await chrome.storage.local.set({ channelName: message.channelName });
            sendResponse({ success: true });
          } catch (error) {
            console.error('❌ Erro ao salvar nome do canal:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'config_updated':
          try {
            console.log('🔄 Configuração atualizada recebida:', message.config);
            
            // Reinicializa os serviços com a nova configuração
            if (configInstance) {
              await configInstance.loadEnvironment();
              console.log('✅ Config recarregado');
            }
            
            // Reinicializa a automação se estiver ativa
            if (automation) {
              console.log('🔄 Reinicializando automação com nova configuração...');
              await initializeAutomation();
            }
            
            sendResponse({ success: true });
          } catch (error) {
            console.error('❌ Erro ao processar atualização de configuração:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'getScheduledProducts':
          try {
            console.log('🔄 Solicitação de produtos agendados recebida');

            if (!automation || !automation.supabaseClient) {
              await initializeAutomation();
            }

            if (!automation || !automation.supabaseClient) {
              throw new Error('Cliente Supabase não inicializado');
            }

            const products = (await automation.supabaseClient.getScheduledProducts({
              enviado_whatsapp: false
            })) || [];

            console.log('✅ Produtos agendados encontrados:', products.length);
            sendResponse({ success: true, products });
          } catch (error) {
            console.error('❌ Erro ao obter produtos agendados:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'testConnection':
          try {
            if (!automation) {
              await initializeAutomation();
            }

            if (!automation || !automation.supabaseClient) {
              await initializeAutomation();
            }

            if (!automation || !automation.supabaseClient) {
              throw new Error('Cliente Supabase não inicializado');
            }

            const count = await automation.supabaseClient.getScheduledProductsCount();
            sendResponse({ success: true, count });
          } catch (error) {
            console.error('❌ Erro no teste de conexão:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'getAppController':
          try {
            console.log('🔄 Solicitação de AppController recebida');
            
            // Verifica se os serviços estão inicializados
            if (!authServiceInstance || !appControllerInstance) {
              console.log('⚠️ Serviços não inicializados, inicializando...');
              await initializeServices();
            }

            if (appControllerInstance && appControllerInstance.isReady()) {
              console.log('✅ AppController disponível e pronto');
              
              // Obtém os controllers de forma segura
              const productController = appControllerInstance.getProductController();
              const authController = appControllerInstance.getAuthController();
              
              sendResponse({ 
                success: true, 
                appController: {
                  // Dados serializáveis em vez de métodos
                  isReady: true,
                  hasProductController: !!productController,
                  hasAuthController: !!authController,
                  debugInfo: appControllerInstance.getDebugInfo()
                },
                // Envia os controllers separadamente
                authService: authServiceInstance,
                productController: productController ? {
                  // Métodos serializáveis do ProductController
                  available: true
                } : null
              });
            } else {
              console.error('❌ AppController não está pronto');
              sendResponse({ 
                success: false, 
                error: 'AppController não está pronto' 
              });
            }
          } catch (error) {
            console.error('❌ Erro ao obter AppController:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'auth_signIn':
          try {
            console.log('🔐 Tentativa de login recebida');
            
            if (!authServiceInstance) {
              await initializeServices();
            }

            if (!authServiceInstance) {
              throw new Error('AuthService não disponível');
            }

            const { email, password } = message;
            console.log('📧 Fazendo login para:', email);
            
            const result = await authServiceInstance.signIn(email, password);
            console.log('✅ Login realizado:', result.user?.email);
            
            sendResponse({ 
              success: true, 
              user: result.user,
              session: result.session 
            });
          } catch (error) {
            console.error('❌ Erro no login:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'auth_signOut':
          try {
            console.log('🚪 Tentativa de logout recebida');
            
            if (!authServiceInstance) {
              throw new Error('AuthService não disponível');
            }

            await authServiceInstance.signOut();
            console.log('✅ Logout realizado');
            
            sendResponse({ success: true });
          } catch (error) {
            console.error('❌ Erro no logout:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        case 'auth_getSession':
          try {
            console.log('🔍 Verificando sessão atual');
            
            if (!authServiceInstance) {
              await initializeServices();
            }

            if (!authServiceInstance) {
              throw new Error('AuthService não disponível');
            }

            const session = await authServiceInstance.getSession();
            console.log('📋 Sessão atual:', session?.user?.email || 'Não autenticado');
            
            sendResponse({ 
              success: true, 
              session: session,
              user: session?.user || null
            });
          } catch (error) {
            console.error('❌ Erro ao verificar sessão:', error);
            sendResponse({ success: false, error: error.message });
          }
          break;

        default:
          console.log('⚠️ Ação não reconhecida:', message.action);
          sendResponse({ error: 'Ação não reconhecida' });
      }
    } catch (error) {
      console.error('❌ Erro no background script:', error);
      sendResponse({ error: error.message });
    }
  })();

  // Retorna true para indicar que a resposta será enviada de forma assíncrona
  return true;
});

// Listener para quando abas são fechadas
chrome.tabs.onRemoved.addListener((tabId) => {
  if (automation && tabId === automation.whatsappTabId) {
    automation.whatsappTabId = null;
    console.log('Aba do WhatsApp Web foi fechada');
  }
});

// Variável para controlar estado do sidebar
let sidebarStates = new Map(); // tabId -> boolean (true = aberto, false = fechado)

// Função para toggle do sidebar
async function toggleSidebar(tabId) {
  try {
    const isOpen = sidebarStates.get(tabId) || false;

    if (isOpen) {
      // Fechar sidebar - definir como fechado no estado
      sidebarStates.set(tabId, false);
      // Não há API direta para fechar, então apenas atualizamos o estado
      console.log('Sidebar marcado como fechado para tab:', tabId);
    } else {
      // Abrir sidebar
      await chrome.sidePanel.open({ tabId });
      sidebarStates.set(tabId, true);
      console.log('Sidebar aberto para tab:', tabId);
    }
  } catch (error) {
    console.error('Erro ao fazer toggle do sidebar:', error);
    // Se der erro ao abrir, resetar o estado
    sidebarStates.set(tabId, false);
  }
}

// Listener para comandos de teclado
chrome.commands.onCommand.addListener(async (command) => {
  try {
    if (command === '_execute_action') {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      await toggleSidebar(tab.id);
    }
  } catch (error) {
    console.error('Erro ao processar comando:', error);
  }
});

// Listener para clique no ícone da extensão
chrome.action.onClicked.addListener(async (tab) => {
  try {
    await toggleSidebar(tab.id);
  } catch (error) {
    console.error('Erro ao fazer toggle do sidebar:', error);
  }
});

// Listener para quando abas são fechadas - limpar estado do sidebar
chrome.tabs.onRemoved.addListener((tabId) => {
  if (automation && tabId === automation.whatsappTabId) {
    automation.whatsappTabId = null;
    console.log('Aba do WhatsApp Web foi fechada');
  }
  // Limpar estado do sidebar para esta aba
  sidebarStates.delete(tabId);
});
