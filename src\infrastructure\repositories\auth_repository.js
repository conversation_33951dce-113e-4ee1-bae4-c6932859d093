/**
 * Repositório para operações de autenticação
 */
export class AuthRepository {
  constructor(supabaseService) {
    this.supabaseService = supabaseService;
  }

  /**
   * Realiza login usando o SDK do Supabase Auth
   * @param {string} email 
   * @param {string} password 
   * @returns {Promise<{user: any, session: any}>}
   */
  async login(email, password) {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        throw new Error(`Erro no login: ${error.message}`);
      }

      if (data.user && data.session) {
        await this.saveAuthData(data.session.access_token, data.user);
      }

      return {
        user: data.user,
        session: data.session
      };
    } catch (error) {
      throw new Error(`Falha no login: ${error.message}`);
    }
  }

  /**
   * Realiza logout usando o SDK do Supabase Auth
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      const { error } = await this.supabaseService.getClient().auth.signOut();

      if (error) {
        throw new Error(`Erro no logout: ${error.message}`);
      }

      // Limpar dados locais
      await this.clearAuthData();
    } catch (error) {
      // Mesmo com erro, limpar dados locais
      await this.clearAuthData();
      throw new Error(`Falha no logout: ${error.message}`);
    }
  }

  /**
   * Limpa dados de autenticação do localStorage
   * @returns {Promise<void>}
   */
  async clearAuthData() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove(['auth_token', 'user_data']);
      } else {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
      }
    } catch (error) {
      console.error('Erro ao limpar dados de autenticação:', error);
    }
  }

  /**
   * Obtém usuário atual usando o SDK do Supabase Auth
   * @returns {Promise<any>}
   */
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await this.supabaseService.getClient().auth.getUser();

      if (error) {
        // Se houver erro, tentar obter do localStorage
        return await this.getUserFromStorage();
      }

      return user;
    } catch (error) {
      // Fallback para localStorage
      return await this.getUserFromStorage();
    }
  }

  /**
   * Obtém usuário do localStorage como fallback
   * @returns {Promise<any>}
   */
  async getUserFromStorage() {
    try {
      let userData = null;
      
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['user_data']);
        userData = result.user_data;
      } else {
        const stored = localStorage.getItem('user_data');
        userData = stored ? JSON.parse(stored) : null;
      }

      return userData;
    } catch (error) {
      console.error('Erro ao obter usuário do storage:', error);
      return null;
    }
  }

  /**
   * Salva dados de autenticação no localStorage
   * @param {string} token 
   * @param {Object} userData 
   */
  saveAuthData(token, userData) {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('promobell_auth_token', token);
      localStorage.setItem('promobell_user_data', JSON.stringify(userData));
    }
    
    this.supabaseService.setToken(token);
  }

  /**
   * Atualiza token usando o SDK do Supabase Auth
   * @returns {Promise<string|null>}
   */
  async refreshToken() {
    try {
      const { data, error } = await this.supabaseService.getClient().auth.refreshSession();

      if (error) {
        throw new Error(`Erro ao atualizar token: ${error.message}`);
      }

      if (data.session) {
        await this.saveAuthData(data.session.access_token, data.session.user);
        return data.session.access_token;
      }

      return null;
    } catch (error) {
      console.error('Erro ao atualizar token:', error);
      return null;
    }
  }

  /**
   * Verifica se há dados de autenticação salvos
   * @returns {boolean}
   */
  hasStoredAuth() {
    if (typeof localStorage === 'undefined') {
      return false;
    }

    const token = localStorage.getItem('promobell_auth_token');
    const userData = localStorage.getItem('promobell_user_data');
    
    return !!(token && userData);
  }
}