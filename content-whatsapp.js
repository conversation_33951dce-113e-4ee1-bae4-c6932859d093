// Content Script para WhatsApp Web
// Responsável por enviar mensagens automaticamente no canal especificado

(function () {
  'use strict';

  class WhatsAppController {
    constructor() {
      this.isReady = false;
      this.currentChannel = null;
      this.messageQueue = [];
      this.init();
    }

    async init() {
      console.log('WhatsApp Controller inicializado');

      // Aguarda o WhatsApp carregar completamente
      await this.waitForWhatsAppLoad();

      // Monitora mudanças na interface
      this.observeChanges();

      this.isReady = true;
      console.log('WhatsApp Web está pronto para automação');
    }

    // Aguarda o WhatsApp Web carregar completamente
    async waitForWhatsAppLoad() {
      return new Promise((resolve) => {
        const checkLoad = () => {
          // Verifica se os elementos principais estão carregados
          const chatList = document.querySelector('[data-testid="chat-list"]');
          const searchBox = document.querySelector('[data-testid="chat-list-search"]');

          if (chatList && searchBox) {
            console.log('WhatsApp Web carregado');
            resolve();
          } else {
            setTimeout(checkLoad, 1000);
          }
        };

        checkLoad();
      });
    }

    // Monitora mudanças na interface do WhatsApp
    observeChanges() {
      const observer = new MutationObserver((mutations) => {
        // Pode ser usado para detectar quando mensagens são enviadas
        // ou quando a interface muda
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }

    // Detecta se é um ID de canal (formato: números e letras, geralmente 22-25 caracteres)
    isChannelId(input) {
      // Remove prefixos comuns de links do WhatsApp
      const cleanInput = input.replace(/https?:\/\/whatsapp\.com\/channel\//, '');
      // Verifica se é um ID válido (apenas números e letras, 20-30 caracteres)
      return /^[A-Za-z0-9]{20,30}$/.test(cleanInput);
    }

    // Extrai o ID do canal de um link
    extractChannelId(input) {
      const match = input.match(/(?:https?:\/\/whatsapp\.com\/channel\/)?([A-Za-z0-9]{20,30})/);
      return match ? match[1] : input;
    }

    // Busca canal por ID usando link direto
    async findChannelById(channelId) {
      try {
        console.log(`Abrindo canal por ID: ${channelId}`);

        // Constrói o link do canal
        const channelUrl = `https://whatsapp.com/channel/${channelId}`;

        // Abre o link em uma nova aba temporária
        const newTab = window.open(channelUrl, '_blank');

        // Aguarda um pouco para o link processar
        await this.sleep(3000);

        // Fecha a aba temporária
        if (newTab) {
          newTab.close();
        }

        // Aguarda o WhatsApp processar e mostrar o canal
        await this.sleep(2000);

        // Verifica se o canal foi aberto na aba atual
        const activeChat = document.querySelector('[data-testid="conversation-header"]');
        if (activeChat) {
          const channelTitle = activeChat.querySelector('[data-testid="conversation-info-header-chat-title"]');
          if (channelTitle) {
            console.log(`Canal aberto: ${channelTitle.textContent}`);
            this.currentChannel = channelTitle.textContent;
            return true;
          }
        }

        throw new Error(`Não foi possível abrir o canal com ID: ${channelId}`);

      } catch (error) {
        console.error('Erro ao buscar canal por ID:', error);
        return false;
      }
    }

    // Busca um canal/contato pelo nome ou ID
    async findChannel(channelInput) {
      try {
        console.log(`Procurando canal: ${channelInput}`);

        // Verifica se é um ID de canal ou link
        if (this.isChannelId(channelInput)) {
          const channelId = this.extractChannelId(channelInput);
          return await this.findChannelById(channelId);
        }

        // Busca por nome (método original)
        return await this.findChannelByName(channelInput);

      } catch (error) {
        console.error('Erro ao buscar canal:', error);
        return false;
      }
    }

    // Busca canal por nome (método original renomeado)
    async findChannelByName(channelName) {
      try {
        console.log(`Procurando canal por nome: ${channelName}`);

        // Clica na caixa de pesquisa
        const searchBox = document.querySelector('[data-testid="chat-list-search"]');
        if (!searchBox) {
          throw new Error('Caixa de pesquisa não encontrada');
        }

        // Limpa pesquisa anterior
        searchBox.click();
        await this.sleep(500);

        // Seleciona todo o texto e substitui
        searchBox.focus();
        document.execCommand('selectAll');
        document.execCommand('insertText', false, channelName);

        await this.sleep(1500); // Aguarda resultados da pesquisa

        // Procura pelo canal nos resultados
        const chatItems = document.querySelectorAll('[data-testid="chat-list"] [data-testid="cell-frame-container"]');

        for (const item of chatItems) {
          const titleElement = item.querySelector('[data-testid="cell-frame-title"]');
          if (titleElement && titleElement.textContent.trim().toLowerCase().includes(channelName.toLowerCase())) {
            console.log(`Canal encontrado: ${titleElement.textContent}`);
            item.click();
            await this.sleep(1000);
            this.currentChannel = channelName;
            return true;
          }
        }

        throw new Error(`Canal "${channelName}" não encontrado`);

      } catch (error) {
        console.error('Erro ao buscar canal por nome:', error);
        return false;
      }
    }

    // Envia uma mensagem de texto
    async sendTextMessage(message) {
      try {
        // Encontra a caixa de mensagem
        const messageBox = document.querySelector('[data-testid="conversation-compose-box-input"]');
        if (!messageBox) {
          throw new Error('Caixa de mensagem não encontrada');
        }

        // Foca na caixa de mensagem
        messageBox.focus();
        await this.sleep(300);

        // Insere o texto
        document.execCommand('insertText', false, message);
        await this.sleep(500);

        // Envia a mensagem (Enter)
        const event = new KeyboardEvent('keydown', {
          key: 'Enter',
          code: 'Enter',
          keyCode: 13,
          which: 13,
          bubbles: true
        });

        messageBox.dispatchEvent(event);
        await this.sleep(1000);

        console.log('Mensagem de texto enviada');
        return true;

      } catch (error) {
        console.error('Erro ao enviar mensagem de texto:', error);
        return false;
      }
    }

    // Envia uma imagem via URL
    async sendImageMessage(imageUrl) {
      try {
        console.log('Enviando imagem:', imageUrl);

        // Encontra o botão de anexo
        const attachButton = document.querySelector('[data-testid="clip"]');
        if (!attachButton) {
          throw new Error('Botão de anexo não encontrado');
        }

        attachButton.click();
        await this.sleep(500);

        // Simula o envio de imagem via URL (método alternativo)
        // Como o WhatsApp Web não permite envio direto via URL,
        // vamos tentar uma abordagem diferente

        // Cria um elemento de input file temporário
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.style.display = 'none';
        document.body.appendChild(input);

        // Baixa a imagem e converte para blob
        const response = await fetch(imageUrl);
        const blob = await response.blob();

        // Cria um arquivo a partir do blob
        const file = new File([blob], 'produto.jpg', { type: blob.type });

        // Simula a seleção do arquivo
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        input.files = dataTransfer.files;

        // Dispara o evento de mudança
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);

        // Remove o elemento temporário
        document.body.removeChild(input);

        await this.sleep(2000); // Aguarda o upload

        // Procura pelo botão de envio da imagem
        const sendImageButton = document.querySelector('[data-testid="send"]');
        if (sendImageButton) {
          sendImageButton.click();
          await this.sleep(1000);
          console.log('Imagem enviada');
          return true;
        }

        return false;

      } catch (error) {
        console.error('Erro ao enviar imagem:', error);
        return false;
      }
    }

    // Método principal para enviar mensagem completa
    async sendMessage(channelName, message, imageUrl = null) {
      try {
        if (!this.isReady) {
          throw new Error('WhatsApp Web ainda não está pronto');
        }

        // Busca o canal
        const channelFound = await this.findChannel(channelName);
        if (!channelFound) {
          throw new Error(`Canal "${channelName}" não encontrado`);
        }

        // Envia a imagem primeiro (se fornecida)
        if (imageUrl) {
          const imageSent = await this.sendImageMessage(imageUrl);
          if (imageSent) {
            await this.sleep(5000); // Aguarda 5 segundos para reconhecimento da imagem
          }
        }

        // Envia a mensagem de texto
        const textSent = await this.sendTextMessage(message);

        if (textSent) {
          console.log('Mensagem completa enviada com sucesso');
          return { success: true };
        } else {
          throw new Error('Falha ao enviar mensagem de texto');
        }

      } catch (error) {
        console.error('Erro ao enviar mensagem completa:', error);
        return { success: false, error: error.message };
      }
    }

    // Utilitário para aguardar
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Verifica se está no canal correto
    async checkCurrentChannel(targetChannelName) {
      try {
        console.log('🔍 Verificando canal atual...');

        // Aguarda a interface carregar
        await this.waitForElement('[data-testid="conversation-info-header"]', 5000);

        // Busca pelo título do canal atual
        const channelTitleSelectors = [
          '[data-testid="conversation-info-header-chat-title"]',
          '[data-testid="conversation-title"]',
          '.chat-title',
          '[title*="canal"]',
          '[aria-label*="canal"]'
        ];

        let currentChannelName = null;

        for (const selector of channelTitleSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            currentChannelName = element.textContent || element.title || element.getAttribute('aria-label');
            if (currentChannelName) {
              console.log('📱 Canal atual encontrado:', currentChannelName);
              break;
            }
          }
        }

        if (!currentChannelName) {
          console.log('❌ Não foi possível identificar o canal atual');
          return false;
        }

        // Verifica se corresponde ao canal alvo
        const isMatch = currentChannelName.toLowerCase().includes(targetChannelName.toLowerCase()) ||
          targetChannelName.toLowerCase().includes(currentChannelName.toLowerCase());

        console.log('🔍 Comparação de canais:');
        console.log('  - Canal atual:', currentChannelName);
        console.log('  - Canal alvo:', targetChannelName);
        console.log('  - Corresponde:', isMatch);

        return isMatch;

      } catch (error) {
        console.error('❌ Erro ao verificar canal atual:', error);
        return false;
      }
    }

    // Navega para o canal especificado
    async navigateToChannel(channelName) {
      try {
        console.log('🧭 Navegando para o canal:', channelName);

        // Primeiro, tenta buscar o canal
        const searchSuccess = await this.searchChannel(channelName);

        if (!searchSuccess) {
          console.log('❌ Não foi possível encontrar o canal na busca');
          return false;
        }

        // Aguarda um pouco para a busca completar
        await this.sleep(1000);

        // Procura pelo resultado da busca e clica
        const searchResultSelectors = [
          '[data-testid="chat-list"] [data-testid="cell-frame-container"]',
          '.chat-list-item',
          '[role="listitem"]'
        ];

        for (const selector of searchResultSelectors) {
          const results = document.querySelectorAll(selector);

          for (const result of results) {
            const titleElement = result.querySelector('[data-testid="conversation-title"], .chat-title, [title]');
            if (titleElement) {
              const resultTitle = titleElement.textContent || titleElement.title;

              if (resultTitle && (
                resultTitle.toLowerCase().includes(channelName.toLowerCase()) ||
                channelName.toLowerCase().includes(resultTitle.toLowerCase())
              )) {
                console.log('✅ Canal encontrado nos resultados, clicando...');
                result.click();

                // Aguarda a navegação completar
                await this.sleep(2000);

                // Verifica se realmente navegou para o canal correto
                const isCorrectChannel = await this.checkCurrentChannel(channelName);

                if (isCorrectChannel) {
                  console.log('✅ Navegação para o canal bem-sucedida');
                  return true;
                }
              }
            }
          }
        }

        console.log('❌ Canal não encontrado nos resultados da busca');
        return false;

      } catch (error) {
        console.error('❌ Erro ao navegar para o canal:', error);
        return false;
      }
    }

    // Busca por um canal específico
    async searchChannel(channelName) {
      try {
        console.log('🔍 Buscando canal:', channelName);

        // Clica no botão de busca
        const searchButton = await this.waitForElement('[data-testid="search"], [aria-label*="Pesquisar"], .search-button', 5000);
        if (!searchButton) {
          console.log('❌ Botão de busca não encontrado');
          return false;
        }

        searchButton.click();
        await this.sleep(500);

        // Encontra o campo de busca
        const searchInput = await this.waitForElement('[data-testid="chat-list-search"], input[placeholder*="Pesquisar"], .search-input', 3000);
        if (!searchInput) {
          console.log('❌ Campo de busca não encontrado');
          return false;
        }

        // Limpa o campo e digita o nome do canal
        searchInput.value = '';
        searchInput.focus();

        // Simula digitação
        for (const char of channelName) {
          searchInput.value += char;
          searchInput.dispatchEvent(new Event('input', { bubbles: true }));
          await this.sleep(50);
        }

        // Pressiona Enter
        searchInput.dispatchEvent(new KeyboardEvent('keydown', { key: 'Enter', bubbles: true }));

        console.log('✅ Busca realizada com sucesso');
        return true;

      } catch (error) {
        console.error('❌ Erro ao buscar canal:', error);
        return false;
      }
    }

    // Limpa a pesquisa
    async clearSearch() {
      try {
        const searchBox = document.querySelector('[data-testid="chat-list-search"]');
        if (searchBox) {
          searchBox.click();
          await this.sleep(300);
          searchBox.focus();
          document.execCommand('selectAll');
          document.execCommand('delete');

          // Pressiona Escape para fechar a pesquisa
          const escEvent = new KeyboardEvent('keydown', {
            key: 'Escape',
            code: 'Escape',
            keyCode: 27,
            which: 27,
            bubbles: true
          });
          searchBox.dispatchEvent(escEvent);
        }
      } catch (error) {
        console.error('Erro ao limpar pesquisa:', error);
      }
    }

    // Aguarda um elemento aparecer na página
    async waitForElement(selector, timeout = 10000) {
      return new Promise((resolve) => {
        const startTime = Date.now();

        const checkElement = () => {
          const element = document.querySelector(selector);

          if (element) {
            resolve(element);
          } else if (Date.now() - startTime > timeout) {
            console.log(`⏰ Timeout aguardando elemento: ${selector}`);
            resolve(null);
          } else {
            setTimeout(checkElement, 100);
          }
        };

        checkElement();
      });
    }
  }

  // Instância global do controller
  const whatsappController = new WhatsAppController();

  // Listener para mensagens do background script
  chrome.runtime.onMessage.addListener(async (message, sender, sendResponse) => {
    console.log('Mensagem recebida no content script:', message);

    try {
      switch (message.action) {
        case 'sendMessage':
          const result = await whatsappController.sendMessage(
            message.channelName,
            message.message,
            message.imageUrl
          );

          // Limpa a pesquisa após enviar
          await whatsappController.clearSearch();

          sendResponse(result);
          break;

        case 'checkReady':
          sendResponse({ ready: whatsappController.isReady });
          break;

        case 'getCurrentChannel':
          sendResponse({ channel: whatsappController.currentChannel });
          break;

        case 'checkChannel':
          const isCorrectChannel = await whatsappController.checkCurrentChannel(message.channelName);
          sendResponse({ success: isCorrectChannel });
          break;

        case 'navigateToChannel':
          const navigationSuccess = await whatsappController.navigateToChannel(message.channelName);
          sendResponse({ success: navigationSuccess });
          break;

        default:
          sendResponse({ success: false, error: 'Ação não reconhecida' });
      }
    } catch (error) {
      console.error('Erro no content script:', error);
      sendResponse({ success: false, error: error.message });
    }

    return true; // Mantém o canal de resposta aberto
  });

  // Notifica que o content script foi carregado
  console.log('Content script do WhatsApp Web carregado');

})();