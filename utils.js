// Utilitários para formatação e manipulação de dados

class MessageFormatter {
  // Formata preço no padrão brasileiro
  static formatPrice(price) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  }

  // Gera mensagem do WhatsApp baseada no modelo Flutter
  static generateWhatsAppMessage(product) {
    const currentPriceFormatted = this.formatPrice(product.preco_atual);
    const oldPriceFormatted = this.formatPrice(product.preco_antigo);

    // Determinar informações de frete
    const shippingInfo = product.frete ? 'Grátis' : 'Consulte no site';

    // Determinar informações de cupom
    const couponInfo = product.cupom && typeof product.cupom === 'string' && product.cupom.trim() !== ''
      ? product.cupom
      : 'Indisponível';

    const productLink = `https://promobell.com.br/product?id=${product.id}?utm_source=whatsapp&utm_medium=social&utm_campaign=lancamento_app`;

    // Montar a mensagem seguindo o padrão solicitado
    let message = '';
    message += product.titulo + '\n\n';

    if (product.plataforma !== 'Mercado Livre') {
      message += `Compre na *${product.plataforma}*\n\n`;
    } else {
      message += `Compre no *${product.plataforma}*\n\n`;
    }

    // Só mostrar preço antigo se for diferente do atual e maior que 0
    if (product.preco_antigo > 0 && product.preco_antigo !== product.preco_atual) {
      message += `De ~${oldPriceFormatted}~\n`;
    }
    message += `Por *${currentPriceFormatted}*\n\n`;
    message += `🚚 Frete: ${shippingInfo}\n`;
    message += `🎟️ Cupom: ${couponInfo}\n\n`;
    message += productLink;

    return message;
  }
}

class DateUtils {
  // Converte string de data para objeto Date
  static parseDate(dateString) {
    return new Date(dateString);
  }

  // Verifica se é hora de enviar o produto
  static isTimeToSend(scheduleDate) {
    const now = new Date();
    const scheduled = this.parseDate(scheduleDate);

    // Considera uma margem de 1 minuto para execução
    const timeDiff = Math.abs(now.getTime() - scheduled.getTime());
    return timeDiff <= 60000; // 60 segundos
  }

  // Formata data para exibição
  static formatDate(date, { timeZone = 'America/Bahia', ...overrides } = {}) {
    const parsedDate = new Date(date);

    if (Number.isNaN(parsedDate.getTime())) {
      return '';
    }

    const baseOptions = {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hourCycle: 'h23',
      timeZone
    };

    const formatter = new Intl.DateTimeFormat('pt-BR', { ...baseOptions, ...overrides, timeZone });
    return formatter.format(parsedDate);
  }
}

class SupabaseClient {
  constructor(options = null, anonKey = null) {
    this.config = null;
    this.url = null;
    this.anonKey = null;
    this.serviceRoleKey = null;
    this.realtimeChannel = null;
    this.realtimeCallbacks = [];
    this.supabaseClient = null;
    this.serviceClient = null;
    this.realtimeClient = null;

    this.applyOptions(options, anonKey);
  }

  applyOptions(options, anonKey) {
    if (options && typeof options === 'object' && !Array.isArray(options)) {
      const { url = null, anonKey: key = null, serviceRoleKey = null, config: providedConfig = null } = options;
      this.url = url;
      this.anonKey = key;
      if (serviceRoleKey) {
        this.serviceRoleKey = typeof serviceRoleKey === 'string' ? serviceRoleKey.trim() : serviceRoleKey;
      }
      if (providedConfig) {
        this.config = providedConfig;
      }
    } else {
      this.url = options;
      this.anonKey = anonKey;
    }
  }

  setConfig(configInstance) {
    if (configInstance) {
      this.config = configInstance;
    }
    return this;
  }

  async ensureConfig() {
    let resolvedConfig = this.config;

    if (!resolvedConfig && typeof config !== 'undefined') {
      resolvedConfig = config;
    }

    if (!resolvedConfig && typeof Config !== 'undefined') {
      resolvedConfig = new Config();
    }

    if (!resolvedConfig) {
      console.warn('⚠️ SupabaseClient: Nenhuma instância de Config disponível');
      return null;
    }

    if (!resolvedConfig.isInitialized && typeof resolvedConfig.initialize === 'function') {
      await resolvedConfig.initialize();
    }

    const envLoader = resolvedConfig.envLoader;
    if (
      typeof resolvedConfig.loadEnvironment === 'function' &&
      envLoader &&
      typeof envLoader.isEnvironmentLoaded === 'function' &&
      !envLoader.isEnvironmentLoaded()
    ) {
      await resolvedConfig.loadEnvironment();
    }

    this.config = resolvedConfig;
    return resolvedConfig;
  }

  // Verifica se está em um canal do WhatsApp
  isWhatsAppChannel() {
    const url = window.location.href;
    console.log('🔍 Verificando URL atual:', url);

    // Verificar se é uma URL do WhatsApp
    const isWhatsApp = url.includes('web.whatsapp.com') || url.includes('whatsapp.com');

    if (!isWhatsApp) {
      console.log('❌ Não é uma página do WhatsApp');
      return false;
    }

    // Verificar se está em um canal específico
    const isChannel = url.includes('/channel/');

    if (!isChannel) {
      console.log('❌ Não está em um canal do WhatsApp');
      return false;
    }

    console.log('✅ Está em um canal do WhatsApp');
    return true;
  }

  // Extrai o ID do canal da URL atual
  getCurrentChannelId() {
    const url = window.location.href;
    console.log('🔍 Extraindo ID do canal da URL:', url);

    // Padrão para extrair ID do canal: /channel/ID
    const channelMatch = url.match(/\/channel\/([^\/\?#]+)/);

    if (channelMatch && channelMatch[1]) {
      const channelId = channelMatch[1];
      console.log('📱 ID do canal encontrado:', channelId);
      return channelId;
    }

    console.log('❌ ID do canal não encontrado na URL');
    return null;
  }

  // Verifica se o canal atual corresponde ao canal configurado
  isTargetChannel(configuredChannel) {
    if (!configuredChannel) {
      console.log('⚠️ Nenhum canal configurado');
      return false;
    }

    const currentChannelId = this.getCurrentChannelId();
    if (!currentChannelId) {
      console.log('❌ Não foi possível obter ID do canal atual');
      return false;
    }

    console.log('🔍 Comparando canais:');
    console.log('  - Canal atual:', currentChannelId);
    console.log('  - Canal configurado:', configuredChannel);

    // Verificar correspondência exata do ID
    if (currentChannelId === configuredChannel) {
      console.log('✅ Canal corresponde exatamente ao ID configurado');
      return true;
    }

    // Verificar se o canal configurado contém o ID atual (para URLs completas)
    if (configuredChannel.includes(currentChannelId)) {
      console.log('✅ Canal atual encontrado na configuração');
      return true;
    }

    // Verificar se a configuração é uma URL completa e extrair o ID
    const configChannelMatch = configuredChannel.match(/\/channel\/([^\/\?#]+)/);
    if (configChannelMatch && configChannelMatch[1] === currentChannelId) {
      console.log('✅ Canal corresponde ao ID extraído da URL configurada');
      return true;
    }

    // Tentar buscar pelo nome do canal na página (fallback)
    try {
      const channelNameElements = document.querySelectorAll('[data-testid="conversation-info-header-chat-title"], .chat-title, [title*="canal"], [aria-label*="canal"]');

      for (const element of channelNameElements) {
        const channelName = element.textContent || element.title || element.getAttribute('aria-label') || '';
        console.log('🏷️ Nome do canal encontrado na página:', channelName);

        if (channelName.toLowerCase().includes(configuredChannel.toLowerCase()) ||
          configuredChannel.toLowerCase().includes(channelName.toLowerCase())) {
          console.log('✅ Canal corresponde ao nome configurado');
          return true;
        }
      }
    } catch (error) {
      console.log('⚠️ Erro ao buscar nome do canal na página:', error);
    }

    console.log('❌ Canal atual não corresponde ao configurado');
    return false;
  }

  async init() {
    try {
      if (this.supabaseClient) {
        return this.supabaseClient;
      }

      console.log('🔄 SupabaseClient: Iniciando inicialização...');

      const resolvedConfig = await this.ensureConfig();

      if (resolvedConfig) {
        console.log('✅ SupabaseClient: Configuração disponível', {
          isInitialized: resolvedConfig.isInitialized,
          hasEnvLoader: !!resolvedConfig.envLoader,
          envLoaderInitialized: resolvedConfig.envLoader
            ? resolvedConfig.envLoader.isEnvironmentLoaded()
            : false
        });
      } else {
        console.warn('⚠️ SupabaseClient: Prosseguindo sem config pré-carregado');
      }

      let supabaseUrl = this.url;
      let supabaseKey = this.anonKey;

      if (supabaseUrl && typeof supabaseUrl.then === 'function') {
        supabaseUrl = await supabaseUrl;
      }
      if (supabaseKey && typeof supabaseKey.then === 'function') {
        supabaseKey = await supabaseKey;
      }

      if ((!supabaseUrl || !supabaseKey) && resolvedConfig) {
        try {
          if (!supabaseUrl) {
            supabaseUrl = await resolvedConfig.getSupabaseUrl();
          }
          if (!supabaseKey) {
            supabaseKey = await resolvedConfig.getSupabaseAnonKey();
          }
          console.log('📋 SupabaseClient: Credenciais carregadas do config', {
            url: supabaseUrl ? `${supabaseUrl.substring(0, 30)}...` : 'Não encontrada',
            key: supabaseKey ? `${supabaseKey.substring(0, 20)}...` : 'Não encontrada'
          });
        } catch (error) {
          console.error('❌ Erro ao obter credenciais do config:', error);
        }
      }

      supabaseUrl = typeof supabaseUrl === 'string' ? supabaseUrl.trim() : '';
      supabaseKey = typeof supabaseKey === 'string' ? supabaseKey.trim() : '';

      this.url = supabaseUrl;
      this.anonKey = supabaseKey;

      console.log('📊 SupabaseClient: Status final das credenciais:', {
        url: supabaseUrl ? '✅ Presente' : '❌ Ausente',
        key: supabaseKey ? '✅ Presente' : '❌ Ausente',
        sdk: typeof supabase !== 'undefined' ? '✅ Disponível' : '❌ Indisponível',
        urlType: typeof supabaseUrl,
        keyType: typeof supabaseKey
      });

      if (
        supabaseUrl &&
        supabaseKey &&
        typeof supabase !== 'undefined' &&
        typeof supabase.createClient === 'function'
      ) {
        this.supabaseClient = supabase.createClient(supabaseUrl, supabaseKey, {
          db: { schema: 'products_promobell_schema' }
        });
        console.log('✅ Cliente Supabase oficial inicializado com sucesso');

        try {
          const { error } = await this.supabaseClient
            .from('scheduled_products')
            .select('count', { count: 'exact', head: true });

          if (error) {
            console.warn('⚠️ Aviso na conexão de teste:', error.message);
          } else {
            console.log('✅ Conexão com Supabase testada com sucesso');
          }
        } catch (testError) {
          console.warn('⚠️ Falha ao testar conexão com Supabase:', testError);
        }
      } else {
        const missingItems = [];
        if (!supabaseUrl) missingItems.push('URL do Supabase');
        if (!supabaseKey) missingItems.push('Chave do Supabase');
        if (typeof supabase === 'undefined' || typeof supabase.createClient !== 'function') {
          missingItems.push('SDK do Supabase');
        }

        throw new Error(`Configurações não encontradas: ${missingItems.join(', ')}`);
      }

      return this.supabaseClient;
    } catch (error) {
      console.error('❌ Erro ao inicializar SupabaseClient:', error);
      throw error;
    }
  }
  // Inicia escuta em tempo real da tabela scheduled_products
  async startRealtimeListener(callback) {
    if (!this.supabaseClient) {
      await this.init();
    }

    try {
      // Adiciona callback à lista
      if (callback && typeof callback === 'function') {
        this.realtimeCallbacks.push(callback);
      }

      // Se já existe um canal, não criar outro
      if (this.realtimeChannel) {
        console.log('🔄 Canal realtime já existe, adicionando callback');
        return;
      }

      console.log('🚀 Iniciando conexão realtime com Supabase...');

      this.realtimeChannel = this.supabaseClient
        .channel('scheduled-products-changes')
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'products_promobell_schema',
            table: 'scheduled_products'
          },
          (payload) => {
            console.log('📡 Evento realtime recebido:', payload);

            // Processar diferentes tipos de eventos
            this.realtimeCallbacks.forEach(callback => {
              try {
                callback({
                  eventType: payload.eventType,
                  new: payload.new,
                  old: payload.old,
                  table: payload.table,
                  schema: payload.schema
                });
              } catch (error) {
                console.error('❌ Erro no callback realtime:', error);
              }
            });
          }
        )
        .subscribe((status) => {
          console.log('📡 Status da conexão realtime:', status);
          if (status === 'SUBSCRIBED') {
            console.log('✅ Conectado ao realtime do Supabase');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Erro no canal realtime');
            throw new Error('Erro na conexão realtime');
          }
        });

    } catch (error) {
      console.error('❌ Erro ao iniciar monitoramento realtime:', error);
      throw error;
    }
  }

  // Verifica produtos que devem ser enviados no horário atual
  async checkScheduledProductsNow() {
    if (!this.supabaseClient) {
      await this.init();
    }

    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000);

    try {
      const { data: products, error } = await this.supabaseClient
        .from('scheduled_products')
        .select('*')
        .gte('date_schedule', fiveMinutesAgo.toISOString())
        .lte('date_schedule', fiveMinutesFromNow.toISOString())
        .eq('enviado_whatsapp', false)
        .order('date_schedule', { ascending: true });

      if (error) {
        console.error('❌ Erro ao verificar produtos agendados:', error);
        throw error;
      }

      if (products && products.length > 0) {
        console.log(`⏰ Encontrados ${products.length} produtos para enviar agora`);

        // Notificar callbacks para produtos que devem ser enviados
        products.forEach(product => {
          this.realtimeCallbacks.forEach(callback => {
            try {
              callback({
                eventType: 'SCHEDULED_READY',
                product: product,
                timestamp: new Date().toISOString()
              });
            } catch (error) {
              console.error('Erro ao executar callback de produto agendado:', error);
            }
          });
        });
      }
    } catch (error) {
      console.error('❌ Erro ao verificar produtos agendados:', error);
      throw error;
    }
  }

  // Para a escuta em tempo real
  stopRealtimeListener() {
    console.log('🛑 Parando monitoramento realtime...');

    // Parar canal realtime oficial se existir
    if (this.realtimeChannel) {
      this.realtimeChannel.unsubscribe();
      this.realtimeChannel = null;
      console.log('✅ Canal realtime oficial desconectado');
    }

    // Limpar callbacks
    this.realtimeCallbacks = [];
    console.log('✅ Monitoramento realtime parado completamente');
  }

  // Obtém contagem de produtos agendados
  async getScheduledProductsCount() {
    if (!this.supabaseClient) {
      await this.init();
    }

    try {
      console.log('🔍 Buscando contagem de produtos agendados...');

      const { count, error } = await this.supabaseClient
        .from('scheduled_products')
        .select('*', { count: 'exact', head: true });

      if (error) {
        console.error('❌ Erro ao buscar produtos agendados:', error);
        throw error;
      }

      console.log(`📊 Total de produtos agendados: ${count}`);
      return count || 0;
    } catch (error) {
      console.error('❌ Erro ao verificar produtos agendados:', error);
      throw error;
    }
  }

  // Marca produto como enviado
  async markProductAsSent(productId) {
    if (!this.supabaseClient) {
      await this.init();
    }

    try {
      const { error } = await this.supabaseClient
        .from('scheduled_products')
        .update({
          enviado_whatsapp: true,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId);

      if (error) {
        console.error('❌ Erro ao marcar produto como enviado:', error);
        throw error;
      }

      console.log(`✅ Produto ${productId} marcado como enviado (enviado_whatsapp = true)`);

      // Notificar via realtime para outros listeners
      this.realtimeCallbacks.forEach(realtimeCallback => {
        try {
          realtimeCallback({
            eventType: 'PRODUCT_SENT',
            productId: productId,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Erro ao notificar callback realtime:', error);
        }
      });

      return true;
    } catch (error) {
      console.error('❌ Erro ao marcar produto como enviado:', error);
      throw error;
    }
  }

  // Busca produtos agendados com filtros
  async getScheduledProducts(filters = {}) {
    if (!this.supabaseClient) {
      await this.init();
    }

    try {
      let query = this.supabaseClient
        .from('scheduled_products')
        .select('*');

      // Aplicar filtros
      if (filters.enviado_whatsapp !== undefined) {
        query = query.eq('enviado_whatsapp', filters.enviado_whatsapp);
      }

      if (filters.dateFrom) {
        query = query.gte('date_schedule', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.lte('date_schedule', filters.dateTo);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      // Ordenação padrão por data de agendamento
      query = query.order('date_schedule', { ascending: true });

      const { data, error } = await query;

      if (error) {
        console.error('❌ Erro ao buscar produtos agendados:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('❌ Erro ao buscar produtos agendados:', error);
      throw error;
    }
  }
}

// Exporta classes para uso global (Service Workers e páginas)
if (typeof window !== 'undefined') {
  window.MessageFormatter = MessageFormatter;
  window.DateUtils = DateUtils;
  window.SupabaseClient = SupabaseClient;
} else if (typeof self !== 'undefined') {
  // Para Service Workers
  self.MessageFormatter = MessageFormatter;
  self.DateUtils = DateUtils;
  self.SupabaseClient = SupabaseClient;
}

// Para uso em módulos ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    MessageFormatter,
    DateUtils,
    SupabaseClient
  };
}

