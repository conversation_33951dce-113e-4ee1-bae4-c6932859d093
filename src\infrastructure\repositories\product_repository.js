/**
 * Repositório para operações com produtos
 */
export class ProductRepository {
  constructor(supabaseService) {
    this.supabaseService = supabaseService;
  }

  /**
   * Obtém produtos agendados não enviados usando o SDK do Supabase
   * @returns {Promise<any[]>}
   */
  async getScheduledProducts() {
    try {
      const products = await this.supabaseService.select('scheduled_products', {
        enviado_whatsapp: { operator: 'eq', value: false }
      }, {
        order: { field: 'date_schedule', direction: 'asc' },
        limit: 50
      });

      return products || [];
    } catch (error) {
      throw new Error(`Falha ao buscar produtos agendados: ${error.message}`);
    }
  }

  /**
   * Obtém contagem de produtos agendados não enviados usando o SDK do Supabase
   * @returns {Promise<number>}
   */
  async getScheduledProductsCount() {
    try {
      return await this.supabaseService.getCount('scheduled_products', {
        enviado_whatsapp: { operator: 'eq', value: false }
      });
    } catch (error) {
      throw new Error(`Falha ao buscar contagem de produtos: ${error.message}`);
    }
  }

  /**
   * Obtém produto por ID usando o SDK do Supabase
   * @param {string} productId 
   * @returns {Promise<any>}
   */
  async getById(productId) {
    try {
      const products = await this.supabaseService.select('scheduled_products', {
        id: { operator: 'eq', value: productId }
      }, {
        limit: 1
      });

      return products && products.length > 0 ? products[0] : null;
    } catch (error) {
      throw new Error(`Falha ao buscar produto: ${error.message}`);
    }
  }

  /**
   * Marca produto como enviado usando o SDK do Supabase
   * @param {string} productId 
   * @returns {Promise<any>}
   */
  async markAsSent(productId) {
    try {
      const updatedProducts = await this.supabaseService.update(
        'scheduled_products',
        {
          enviado_whatsapp: true,
          updated_at: new Date().toISOString()
        },
        {
          id: { operator: 'eq', value: productId }
        },
        {
          select: '*'
        }
      );

      return updatedProducts && updatedProducts.length > 0 ? updatedProducts[0] : null;
    } catch (error) {
      throw new Error(`Falha ao marcar produto como enviado: ${error.message}`);
    }
  }

  /**
   * Cria novo produto agendado usando o SDK do Supabase
   * @param {Object} productData 
   * @returns {Promise<any>}
   */
  async create(productData) {
    try {
      const newProducts = await this.supabaseService.insert('scheduled_products', {
        ...productData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        select: '*'
      });

      return newProducts && newProducts.length > 0 ? newProducts[0] : null;
    } catch (error) {
      throw new Error(`Falha ao criar produto: ${error.message}`);
    }
  }

  /**
   * Atualiza produto existente usando o SDK do Supabase
   * @param {string} productId 
   * @param {Object} productData 
   * @returns {Promise<any>}
   */
  async update(productId, productData) {
    try {
      const updatedProducts = await this.supabaseService.update(
        'scheduled_products',
        {
          ...productData,
          updated_at: new Date().toISOString()
        },
        {
          id: { operator: 'eq', value: productId }
        },
        {
          select: '*'
        }
      );

      return updatedProducts && updatedProducts.length > 0 ? updatedProducts[0] : null;
    } catch (error) {
      throw new Error(`Falha ao atualizar produto: ${error.message}`);
    }
  }

  /**
   * Remove produto usando o SDK do Supabase
   * @param {string} productId 
   * @returns {Promise<void>}
   */
  async delete(productId) {
    try {
      await this.supabaseService.delete('scheduled_products', {
        id: { operator: 'eq', value: productId }
      });
    } catch (error) {
      throw new Error(`Falha ao remover produto: ${error.message}`);
    }
  }

  /**
   * Obtém produtos por filtros customizados usando o SDK do Supabase
   * @param {Object} filters 
   * @returns {Promise<any[]>}
   */
  async getByFilters(filters = {}) {
    try {
      const products = await this.supabaseService.select('scheduled_products', filters);
      return products || [];
    } catch (error) {
      throw new Error(`Falha ao buscar produtos com filtros: ${error.message}`);
    }
  }
}