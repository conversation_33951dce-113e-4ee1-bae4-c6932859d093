/**
 * View responsável pela gestão de produtos
 */
class ProductsPage {
  constructor() {
    this.products = [];
    this.scheduledProducts = [];
    this.isMonitoring = false;
    this.monitoringInterval = null;

    try {
      this.initialize();
      console.log('✅ ProductsPage inicializada');
    } catch (error) {
      console.error('❌ Erro na inicialização da ProductsPage:', error);
    }
  }

  /**
   * Inicializa a página de produtos
   */
  async initialize() {
    try {
      // Carrega produtos agendados do storage
      await this.loadProducts();
      
      // Inicia monitoramento em tempo real se necessário
      this.startRealtimeMonitoring();
    } catch (error) {
      console.error('Erro na inicialização:', error);
    }
  }

  /**
   * Carrega produtos do storage
   */
  async loadProducts() {
    try {
      const result = await this.getStorageData(['scheduledProducts', 'products']);
      this.scheduledProducts = result.scheduledProducts || [];
      this.products = result.products || [];
      
      console.log(`Produtos carregados: ${this.products.length} total, ${this.scheduledProducts.length} agendados`);
      return this.products;
    } catch (error) {
      console.error('Erro ao carregar produtos:', error);
      return [];
    }
  }

  /**
   * Obtém dados do storage
   */
  async getStorageData(keys) {
    return new Promise((resolve) => {
      chrome.storage.local.get(keys, (result) => {
        resolve(result);
      });
    });
  }

  /**
   * Salva produtos no storage
   */
  async saveProducts() {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set({
        products: this.products,
        scheduledProducts: this.scheduledProducts
      }, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Adiciona produto à agenda
   */
  async scheduleProduct(product, scheduledTime) {
    try {
      const scheduledProduct = {
        ...product,
        id: product.id || this.generateId(),
        scheduledTime: scheduledTime,
        status: 'pending',
        createdAt: new Date().toISOString()
      };

      this.scheduledProducts.push(scheduledProduct);
      await this.saveProducts();
      
      console.log('Produto agendado:', scheduledProduct);
      this.notifyProductsUpdate();
      
      return scheduledProduct;
    } catch (error) {
      console.error('Erro ao agendar produto:', error);
      throw error;
    }
  }

  /**
   * Remove produto da agenda
   */
  async removeScheduledProduct(productId) {
    try {
      const index = this.scheduledProducts.findIndex(p => p.id === productId);
      if (index !== -1) {
        this.scheduledProducts.splice(index, 1);
        await this.saveProducts();
        this.notifyProductsUpdate();
        console.log('Produto removido da agenda:', productId);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao remover produto:', error);
      throw error;
    }
  }

  /**
   * Atualiza status do produto
   */
  async updateProductStatus(productId, status) {
    try {
      const product = this.scheduledProducts.find(p => p.id === productId);
      if (product) {
        product.status = status;
        product.updatedAt = new Date().toISOString();
        
        if (status === 'sent') {
          product.sentAt = new Date().toISOString();
        }
        
        await this.saveProducts();
        this.notifyProductsUpdate();
        console.log(`Status do produto ${productId} atualizado para: ${status}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      throw error;
    }
  }

  /**
   * Obtém produtos pendentes
   */
  getPendingProducts() {
    return this.scheduledProducts.filter(p => p.status === 'pending');
  }

  /**
   * Obtém produtos enviados
   */
  getSentProducts() {
    return this.scheduledProducts.filter(p => p.status === 'sent');
  }

  /**
   * Obtém próximos produtos para envio
   */
  getNextProducts(limit = 5) {
    const now = new Date();
    return this.scheduledProducts
      .filter(p => p.status === 'pending' && new Date(p.scheduledTime) <= now)
      .sort((a, b) => new Date(a.scheduledTime) - new Date(b.scheduledTime))
      .slice(0, limit);
  }

  /**
   * Obtém produtos ordenados por data
   */
  getOrderedProducts() {
    return [...this.scheduledProducts].sort((a, b) => 
      new Date(a.scheduledTime) - new Date(b.scheduledTime)
    );
  }

  /**
   * Obtém contagem de produtos
   */
  getProductsCount() {
    return {
      total: this.scheduledProducts.length,
      pending: this.getPendingProducts().length,
      sent: this.getSentProducts().length
    };
  }

  /**
   * Obtém todos os produtos
   */
  getAllProducts() {
    return this.products;
  }

  /**
   * Inicia monitoramento em tempo real
   */
  startRealtimeMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.checkScheduledProducts();
    }, 30000); // Verifica a cada 30 segundos

    console.log('Monitoramento em tempo real iniciado');
  }

  /**
   * Para monitoramento em tempo real
   */
  stopRealtimeMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('Monitoramento em tempo real parado');
  }

  /**
   * Verifica produtos agendados
   */
  async checkScheduledProducts() {
    try {
      const nextProducts = this.getNextProducts(10);
      if (nextProducts.length > 0) {
        console.log(`${nextProducts.length} produtos prontos para envio`);
        
        // Notifica sobre produtos prontos
        this.notifyProductsReady(nextProducts);
      }
    } catch (error) {
      console.error('Erro ao verificar produtos agendados:', error);
    }
  }

  /**
   * Notifica sobre produtos prontos
   */
  notifyProductsReady(products) {
    // Envia mensagem para o background script
    if (chrome.runtime && chrome.runtime.sendMessage) {
      chrome.runtime.sendMessage({
        type: 'PRODUCTS_READY',
        products: products
      });
    }

    // Dispara evento customizado
    const event = new CustomEvent('productsReady', {
      detail: { products }
    });
    document.dispatchEvent(event);
  }

  /**
   * Notifica atualização de produtos
   */
  notifyProductsUpdate() {
    const event = new CustomEvent('productsUpdated', {
      detail: {
        total: this.scheduledProducts.length,
        pending: this.getPendingProducts().length,
        sent: this.getSentProducts().length
      }
    });
    document.dispatchEvent(event);
  }

  /**
   * Limpa todos os produtos agendados
   */
  async clearAllScheduledProducts() {
    try {
      this.scheduledProducts = [];
      await this.saveProducts();
      this.notifyProductsUpdate();
      console.log('Todos os produtos agendados foram limpos');
      return true;
    } catch (error) {
      console.error('Erro ao limpar produtos:', error);
      throw error;
    }
  }

  /**
   * Importa produtos de um arquivo CSV
   */
  async importProductsFromCSV(csvData) {
    try {
      const lines = csvData.split('\n');
      const headers = lines[0].split(',').map(h => h.trim());
      const products = [];

      for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());
        const product = {};

        headers.forEach((header, index) => {
          product[header] = values[index] || '';
        });

        if (product.name && product.price) {
          product.id = this.generateId();
          product.price = parseFloat(product.price) || 0;
          products.push(product);
        }
      }

      this.products = [...this.products, ...products];
      await this.saveProducts();
      
      console.log(`${products.length} produtos importados do CSV`);
      return products;
    } catch (error) {
      console.error('Erro ao importar CSV:', error);
      throw error;
    }
  }

  /**
   * Exporta produtos para CSV
   */
  exportProductsToCSV() {
    try {
      if (this.scheduledProducts.length === 0) {
        return '';
      }

      const headers = ['id', 'name', 'price', 'scheduledTime', 'status', 'createdAt'];
      const csvContent = [
        headers.join(','),
        ...this.scheduledProducts.map(product => 
          headers.map(header => product[header] || '').join(',')
        )
      ].join('\n');

      return csvContent;
    } catch (error) {
      console.error('Erro ao exportar CSV:', error);
      throw error;
    }
  }

  /**
   * Gera ID único
   */
  generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * Busca produtos por termo
   */
  searchProducts(term) {
    if (!term) return this.products;

    const searchTerm = term.toLowerCase();
    return this.products.filter(product => 
      product.name?.toLowerCase().includes(searchTerm) ||
      product.description?.toLowerCase().includes(searchTerm) ||
      product.category?.toLowerCase().includes(searchTerm)
    );
  }

  /**
   * Filtra produtos por categoria
   */
  filterProductsByCategory(category) {
    if (!category) return this.products;
    return this.products.filter(product => product.category === category);
  }

  /**
   * Obtém estatísticas dos produtos
   */
  getProductsStats() {
    const stats = {
      total: this.scheduledProducts.length,
      pending: this.getPendingProducts().length,
      sent: this.getSentProducts().length,
      failed: this.scheduledProducts.filter(p => p.status === 'failed').length,
      categories: {},
      totalValue: 0
    };

    this.scheduledProducts.forEach(product => {
      // Contagem por categoria
      const category = product.category || 'Sem categoria';
      stats.categories[category] = (stats.categories[category] || 0) + 1;

      // Valor total
      stats.totalValue += parseFloat(product.price) || 0;
    });

    return stats;
  }

  /**
   * Método estático para criar instância global
   */
  static createGlobalInstance() {
    if (typeof window !== 'undefined') {
      window.ProductsPage = new ProductsPage();
      return window.ProductsPage;
    }
    return new ProductsPage();
  }

  /**
   * Cleanup da instância
   */
  cleanup() {
    this.stopRealtimeMonitoring();
    this.products = [];
    this.scheduledProducts = [];
  }
}

// Torna a classe disponível globalmente
window.ProductsPage = ProductsPage;