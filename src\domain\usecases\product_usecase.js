import { Product } from '../entities/product_entity.js';

/**
 * Use Case para operações com produtos
 */
export class ProductUseCase {
  constructor(productRepository) {
    this.productRepository = productRepository;
  }

  /**
   * Obtém produtos agendados não enviados
   * @returns {Promise<Product[]>}
   */
  async getScheduledProducts() {
    try {
      const productsData = await this.productRepository.getScheduledProducts();

      return productsData.map(productData => new Product(productData));
    } catch (error) {
      throw new Error(`Erro ao buscar produtos agendados: ${error.message}`);
    }
  }

  /**
   * Obtém contagem de produtos agendados não enviados
   * @returns {Promise<number>}
   */
  async getScheduledProductsCount() {
    try {
      return await this.productRepository.getScheduledProductsCount();
    } catch (error) {
      throw new Error(`Erro ao buscar contagem de produtos: ${error.message}`);
    }
  }

  /**
   * Marca produto como enviado
   * @param {string} productId 
   * @returns {Promise<Product>}
   */
  async markProductAsSent(productId) {
    try {
      const updatedProductData = await this.productRepository.markAsSent(productId);
      return new Product(updatedProductData);
    } catch (error) {
      throw new Error(`Erro ao marcar produto como enviado: ${error.message}`);
    }
  }

  /**
   * Obtém produto por ID
   * @param {string} productId 
   * @returns {Promise<Product|null>}
   */
  async getProductById(productId) {
    try {
      const productData = await this.productRepository.getById(productId);

      if (!productData) {
        return null;
      }

      return new Product(productData);
    } catch (error) {
      throw new Error(`Erro ao buscar produto: ${error.message}`);
    }
  }

  /**
   * Filtra produtos por status de envio
   * @param {Product[]} products 
   * @param {boolean} sent 
   * @returns {Product[]}
   */
  filterProductsBySentStatus(products, sent = false) {
    return products.filter(product => product.isSent() === sent);
  }

  /**
   * Filtra produtos agendados para o futuro
   * @param {Product[]} products 
   * @returns {Product[]}
   */
  filterFutureScheduledProducts(products) {
    return products.filter(product => product.isScheduledForFuture());
  }

  /**
   * Ordena produtos por data de agendamento
   * @param {Product[]} products 
   * @param {string} order - 'asc' ou 'desc'
   * @returns {Product[]}
   */
  sortProductsByScheduleDate(products, order = 'asc') {
    return products.sort((a, b) => {
      const dateA = new Date(a.date_schedule);
      const dateB = new Date(b.date_schedule);

      return order === 'asc' ? dateA - dateB : dateB - dateA;
    });
  }
}