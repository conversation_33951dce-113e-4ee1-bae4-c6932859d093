/**
 * Entidade Product - Representa um produto agendado
 */
export class Product {
  constructor({ 
    id, 
    title, 
    nome_produto, 
    description, 
    price, 
    date_schedule, 
    enviado_whatsapp = false,
    created_at,
    updated_at 
  }) {
    this.id = id;
    this.title = title;
    this.nome_produto = nome_produto;
    this.description = description;
    this.price = price;
    this.date_schedule = date_schedule;
    this.enviado_whatsapp = enviado_whatsapp;
    this.created_at = created_at;
    this.updated_at = updated_at;
  }

  /**
   * Retorna o nome do produto (prioriza title, depois nome_produto)
   * @returns {string}
   */
  getName() {
    return this.title || this.nome_produto || 'Produto sem nome';
  }

  /**
   * Verifica se o produto já foi enviado
   * @returns {boolean}
   */
  isSent() {
    return this.enviado_whatsapp === true;
  }

  /**
   * Verifica se o produto está agendado para o futuro
   * @returns {boolean}
   */
  isScheduledForFuture() {
    if (!this.date_schedule) return false;
    return new Date(this.date_schedule) > new Date();
  }

  /**
   * Verifica se o produto deve ser enviado agora
   * @returns {boolean}
   */
  shouldBeSentNow() {
    if (this.isSent()) return false;
    if (!this.date_schedule) return false;
    
    const now = new Date();
    const scheduledTime = new Date(this.date_schedule);
    
    // Considera um produto pronto para envio se a data agendada já passou
    return scheduledTime <= now;
  }

  /**
   * Retorna o preço formatado
   * @returns {string}
   */
  getFormattedPrice() {
    if (!this.price) return 'Preço não informado';
    
    const price = parseFloat(this.price);
    if (isNaN(price)) return 'Preço inválido';
    
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  }

  /**
   * Retorna a data de agendamento formatada
   * @returns {string}
   */
  getFormattedScheduleDate() {
    if (!this.date_schedule) return 'Data não informada';
    
    const date = new Date(this.date_schedule);
    if (isNaN(date.getTime())) return 'Data inválida';
    
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  /**
   * Converte a entidade para objeto simples
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      nome_produto: this.nome_produto,
      description: this.description,
      price: this.price,
      date_schedule: this.date_schedule,
      enviado_whatsapp: this.enviado_whatsapp,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }
}