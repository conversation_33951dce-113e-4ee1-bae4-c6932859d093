import { AppController } from '../controllers/app_controller.js';

/**
 * View responsável pela interface de produtos
 */
export class ProductsPage {
  constructor() {
    this.appController = null;
    this.authController = null;
    this.productController = null;
    this.products = [];
    this.updateInterval = null;

    this.initialize();
  }

  /**
   * Inicializa a view de produtos
   */
  async initialize() {
    try {
      // Aguarda um pouco para garantir que o DOM esteja completamente carregado
      await new Promise(resolve => setTimeout(resolve, 100));

      // Inicializa o controller principal
      this.appController = AppController.create();
      await this.appController.initialize();

      // Obtém controllers específicos
      this.authController = this.appController.getAuthController();
      this.productController = this.appController.getProductController();

      // Configura listeners
      this.setupControllerListeners();

      // Verifica se usuário está autenticado
      const isAuthenticated = await this.authController.isAuthenticated();

      if (isAuthenticated) {
        await this.loadProducts();
        await this.startRealtimeMonitoring();
      }

      console.log('✅ ProductsPage inicializada');
    } catch (error) {
      console.error('❌ Erro na inicialização da ProductsPage:', error);
    }
  }

  /**
   * Configura listeners dos controllers
   */
  setupControllerListeners() {
    // Listener para mudanças de autenticação
    this.authController.addAuthStateListener((authState) => {
      if (authState.isAuthenticated) {
        this.loadProducts();
        this.startRealtimeMonitoring();
      } else {
        this.clearProducts();
        this.stopRealtimeMonitoring();
      }
    });

    // Listener para atualizações de produtos
    this.productController.addListener((event) => {
      if (event.type === 'products_updated') {
        this.handleProductsUpdate(event.data.products, event.data.count);
      } else if (event.type === 'products_cleared') {
        this.handleProductsCleared();
      } else if (event.type === 'realtime_update') {
        this.handleRealtimeUpdate(event.data);
      }
    });
  }

  /**
   * Carrega produtos agendados
   */
  async loadProducts() {
    try {
      const isAuthenticated = await this.authController.isAuthenticated();
      if (!isAuthenticated) {
        console.log('⚠️ Usuário não autenticado - não é possível carregar produtos');
        return;
      }

      console.log('🔍 Carregando produtos agendados...');

      // Busca produtos através do controller
      const products = await this.productController.getScheduledProducts();
      this.products = products;

      console.log(`✅ ${this.products.length} produtos agendados carregados`);

      // Notifica sobre a atualização
      this.notifyProductsUpdate();

      return this.products;
    } catch (error) {
      console.error('❌ Erro ao carregar produtos:', error);
      this.products = [];
      return [];
    }
  }

  /**
   * Obtém contagem de produtos agendados
   */
  async getProductsCount() {
    try {
      const isAuthenticated = await this.authController.isAuthenticated();
      if (!isAuthenticated) {
        return 0;
      }

      const count = await this.productController.getScheduledProductsCount();
      return count;
    } catch (error) {
      console.error('❌ Erro ao obter contagem de produtos:', error);
      return 0;
    }
  }

  /**
   * Inicia monitoramento em tempo real
   */
  async startRealtimeMonitoring() {
    try {
      const isAuthenticated = await this.authController.isAuthenticated();
      if (!isAuthenticated) {
        throw new Error('Usuário não autenticado');
      }

      // Para monitoramento anterior se existir
      this.stopRealtimeMonitoring();

      console.log('🔄 Iniciando monitoramento realtime de produtos...');

      // Inicia monitoramento através do controller
      await this.productController.startRealtimeMonitoring();

      // Fallback para polling se realtime não funcionar
      this.startPolling();

      console.log('✅ Monitoramento realtime iniciado');
    } catch (error) {
      console.error('❌ Erro ao iniciar monitoramento realtime:', error);
      // Fallback para polling em caso de erro
      this.startPolling();
    }
  }

  /**
   * Para monitoramento em tempo real
   */
  stopRealtimeMonitoring() {
    try {
      // Para monitoramento através do controller
      this.productController.stopRealtimeMonitoring();

      // Para polling se estiver ativo
      if (this.updateInterval) {
        clearInterval(this.updateInterval);
        this.updateInterval = null;
      }

      console.log('⏹️ Monitoramento realtime parado');
    } catch (error) {
      console.error('❌ Erro ao parar monitoramento realtime:', error);
    }
  }

  /**
   * Inicia polling como fallback
   */
  startPolling() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }

    console.log('🔄 Iniciando polling de produtos (fallback)...');

    this.updateInterval = setInterval(async () => {
      try {
        const newCount = await this.getProductsCount();
        const currentCount = this.products.length;

        if (newCount !== currentCount) {
          console.log(`📊 Mudança detectada: ${currentCount} → ${newCount} produtos`);
          await this.loadProducts();
        }
      } catch (error) {
        console.error('❌ Erro no polling:', error);
      }
    }, 30000); // Polling a cada 30 segundos
  }

  /**
   * Manipula atualizações de produtos
   */
  handleProductsUpdate(products, count) {
    this.products = products || [];
    console.log(`📊 Produtos atualizados: ${count} produtos`);
    this.notifyProductsUpdate();
  }

  /**
   * Manipula limpeza de produtos
   */
  handleProductsCleared() {
    this.products = [];
    console.log('🧹 Produtos limpos');
    this.notifyProductsUpdate();
  }

  /**
   * Manipula atualizações em tempo real
   */
  handleRealtimeUpdate(data) {
    try {
      console.log('📡 Atualização realtime recebida:', data);

      if (data.eventType === 'INSERT') {
        // Novo produto agendado
        this.products.push(data.new);
        console.log('➕ Novo produto agendado adicionado');
      } else if (data.eventType === 'UPDATE') {
        // Produto atualizado
        const index = this.products.findIndex(p => p.id === data.new.id);
        if (index !== -1) {
          this.products[index] = data.new;
          console.log('🔄 Produto agendado atualizado');
        }
      } else if (data.eventType === 'DELETE') {
        // Produto removido
        this.products = this.products.filter(p => p.id !== data.old.id);
        console.log('➖ Produto agendado removido');
      }

      this.notifyProductsUpdate();
    } catch (error) {
      console.error('❌ Erro ao processar atualização realtime:', error);
    }
  }

  /**
   * Notifica sobre atualizações de produtos
   */
  notifyProductsUpdate() {
    try {
      // Dispara evento customizado para atualizar a interface
      const event = new CustomEvent('productsUpdated', {
        detail: {
          products: this.products,
          count: this.products.length
        }
      });

      document.dispatchEvent(event);

      // Também notifica através do window para compatibilidade
      if (typeof window !== 'undefined') {
        window.dispatchEvent(event);
      }
    } catch (error) {
      console.error('❌ Erro ao notificar atualização:', error);
    }
  }

  /**
   * Limpa produtos
   */
  clearProducts() {
    this.products = [];
    this.notifyProductsUpdate();
  }

  /**
   * Obtém produtos ordenados por data de agendamento
   */
  getOrderedProducts() {
    return [...this.products].sort((a, b) => {
      const dateA = new Date(a.date_schedule || a.scheduled_time);
      const dateB = new Date(b.date_schedule || b.scheduled_time);
      return dateA - dateB;
    });
  }

  /**
   * Obtém próximos N produtos
   */
  getNextProducts(limit = 10) {
    const ordered = this.getOrderedProducts();
    return ordered.slice(0, limit);
  }

  /**
   * Obtém todos os produtos
   */
  getAllProducts() {
    return [...this.products];
  }

  /**
   * Obtém contagem atual de produtos
   */
  getCurrentCount() {
    return this.products.length;
  }

  /**
   * Cleanup da view
   */
  cleanup() {
    this.stopRealtimeMonitoring();

    if (this.appController) {
      this.appController.cleanup();
    }
  }

  /**
   * Método estático para criar instância global
   */
  static createGlobalInstance() {
    if (typeof window !== 'undefined') {
      window.ProductsPage = new ProductsPage();
      return window.ProductsPage;
    }
    return new ProductsPage();
  }
}