/**
 * Entidade User - Representa um usuário do sistema
 */
export class User {
  constructor({ id, email, name, token, refreshToken, expiresAt }) {
    this.id = id;
    this.email = email;
    this.name = name;
    this.token = token;
    this.refreshToken = refreshToken;
    this.expiresAt = expiresAt;
  }

  /**
   * Verifica se o token do usuário está válido
   * @returns {boolean}
   */
  isTokenValid() {
    if (!this.token || !this.expiresAt) {
      return false;
    }
    return new Date() < new Date(this.expiresAt);
  }

  /**
   * Verifica se o usuário está autenticado
   * @returns {boolean}
   */
  isAuthenticated() {
    return this.isTokenValid() && !!this.id;
  }

  /**
   * Retorna dados básicos do usuário
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      email: this.email,
      name: this.name,
      isAuthenticated: this.isAuthenticated()
    };
  }
}