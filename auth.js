// Módulo de autenticação para extensão Chrome
// Autenticação direta com Supabase (independente do PromoServer)

class AuthService {
  constructor() {
    this.baseUrl = null;
    this.token = null;
    this.user = null;
    this.permissions = null;
    this.isAuthenticated = false;
    this.storage = null;
    this.config = null;
    this.authStateListeners = [];
    
    // Configurações do Supabase serão carregadas do .env
    this.supabaseUrl = null;
    this.supabaseAnonKey = null;
  }

  // Inicializa o serviço de autenticação
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Inicializa a configuração
      this.config = new Config();
      await this.config.initialize();

      // Cria o cliente Supabase
      this.supabaseClient = await this.config.createSupabaseClient();

      // Configura listener para mudanças de estado de autenticação
      this.supabaseClient.auth.onAuthStateChange((event, session) => {
        this.handleAuthStateChange(event, session);
      });

      // Verifica se há uma sessão ativa
      const { data: { session } } = await this.supabaseClient.auth.getSession();
      if (session) {
        this.currentUser = session.user;
        this.currentSession = session;
        console.log('✅ AuthService: Sessão ativa encontrada:', session.user.email);
      }

      this.isInitialized = true;
      console.log('✅ AuthService: Inicializado com sucesso');
    } catch (error) {
      console.error('❌ AuthService: Erro ao inicializar:', error);
      throw error;
    }
  }

  // Carrega configuração do .env
  async loadConfig() {
    try {
      // Importa e inicializa o config se necessário
      if (typeof config === 'undefined') {
        await this.loadConfigModule();
      }

      await config.initialize();
      this.config = config; // Armazena a referência do config
      const envConfig = await config.get();
      
      this.supabaseUrl = envConfig.SUPABASE_URL;
      this.supabaseAnonKey = envConfig.SUPABASE_ANON_KEY;
      
      console.log('✅ AuthService: Configurações carregadas do .env');
    } catch (error) {
      console.error('❌ AuthService: Erro ao carregar configurações:', error);
      throw error;
    }
  }

  // Carrega o módulo de configuração dinamicamente
  async loadConfigModule() {
    try {
      // Verifica se estamos em um contexto com DOM
      if (typeof document !== 'undefined') {
        return new Promise((resolve, reject) => {
          const script = document.createElement('script');
          script.src = chrome.runtime.getURL('config.js');
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        });
      } else {
        // Em service workers, importa diretamente
        await import(chrome.runtime.getURL('config.js'));
        return Promise.resolve();
      }
    } catch (error) {
      console.error('❌ AuthService: Erro ao carregar config.js:', error);
      throw error;
    }
  }

  // Extrai role do usuário dos metadados
  extractUserRole(user) {
    if (!user) return 'User';
    
    // Verifica se é admin baseado no email
    if (user.email === '<EMAIL>') {
      return 'Admin';
    }
    
    // Verifica metadados do usuário
    if (user.user_metadata && user.user_metadata.role) {
      return user.user_metadata.role;
    }
    
    if (user.app_metadata && user.app_metadata.role) {
      return user.app_metadata.role;
    }
    
    return 'User';
  }

  // Valida token diretamente com Supabase
  async validateToken(token) {
    try {
      const response = await fetch(`${this.supabaseUrl}/auth/v1/user`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'apikey': this.supabaseAnonKey
        }
      });

      if (response.ok) {
        const userData = await response.json();
        return {
          valid: true,
          user: userData
        };
      } else {
        return { valid: false };
      }
    } catch (error) {
      console.error('Erro ao validar token:', error);
      return { valid: false };
    }
  }

  // Método para fazer login com OAuth (Google, GitHub, etc.)
  async signInWithOAuth(provider, options = {}) {
    try {
      if (!this.supabaseClient) {
        throw new Error('AuthService não foi inicializado');
      }

      // Para extensões Chrome, usamos o fluxo PKCE
      const { data, error } = await this.supabaseClient.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: chrome.runtime.getURL('sidebar.html'),
          ...options
        }
      });

      if (error) {
        console.error('❌ AuthService: Erro no login OAuth:', error);
        throw error;
      }

      console.log('✅ AuthService: Login OAuth iniciado');
      return data;
    } catch (error) {
      console.error('❌ AuthService: Erro ao fazer login OAuth:', error);
      throw error;
    }
  }

  // Método para fazer login com email e senha
  async signInWithPassword(email, password) {
    try {
      if (!this.supabaseClient) {
        throw new Error('AuthService não foi inicializado');
      }

      const { data, error } = await this.supabaseClient.auth.signInWithPassword({
        email,
        password
      });

      if (error) {
        console.error('❌ AuthService: Erro no login:', error);
        throw error;
      }

      console.log('✅ AuthService: Login realizado com sucesso');
      return data;
    } catch (error) {
      console.error('❌ AuthService: Erro ao fazer login:', error);
      throw error;
    }
  }

  async login(email, password) {
    try {
      console.log(`🔐 Tentando login com SDK do Supabase para ${email}`);
      
      // Verifica se o config foi inicializado
      if (!this.config) {
        await this.loadConfig();
      }
      
      // Cria cliente Supabase usando o SDK oficial
      const supabaseClient = await this.config.createSupabaseClient();
      
      // Login usando o SDK oficial do Supabase
      const { data, error } = await supabaseClient.auth.signInWithPassword({
        email: email,
        password: password
      });

      if (error) {
        console.error('❌ Erro no login Supabase:', error.message);
        return { success: false, message: error.message };
      }

      if (data.user && data.session) {
        this.token = data.session.access_token;
        this.user = data.user;
        this.userEmail = email;
        this.userProfile = this.extractUserRole(data.user) || 'Manager';
        this.isAuthenticated = true;

        // Salva a sessão no armazenamento persistente
        await this.saveSessionToPersistentStorage({
          token: this.token,
          email: this.userEmail,
          profile: this.userProfile,
          user: data.user,
          session: data.session
        });

        console.log('✅ Login realizado com sucesso usando SDK do Supabase');
        return { success: true, profile: this.userProfile, user: data.user };
      } else {
        console.error('❌ Dados de sessão inválidos');
        return { success: false, message: 'Erro na autenticação' };
      }
    } catch (error) {
      console.error('❌ Erro na requisição de login:', error);
      return { success: false, message: 'Erro de conexão com Supabase' };
    }
  }

  // Carrega dados do usuário autenticado (usando dados do Supabase)
  async loadUserData() {
    try {
      if (!this.user) {
        console.warn('⚠️ Nenhum usuário autenticado para carregar dados');
        return;
      }

      // Os dados do usuário já vêm do Supabase na autenticação
      this.permissions = this.extractPermissionsFromUser(this.user);
      
      // Armazena dados do usuário
      await this.storeUserData(this.user);
      
      console.log('✅ Dados do usuário carregados do Supabase');
    } catch (error) {
      console.error('❌ Erro ao carregar dados do usuário:', error);
      throw error;
    }
  }

  // Extrai permissões do usuário baseado no role
  extractPermissionsFromUser(user) {
    const role = this.extractUserRole(user);
    
    // Define permissões baseadas no role
    const rolePermissions = {
      'Admin': [
        'products:read', 'products:write',
        'scheduling:read', 'scheduling:write',
        'injection:read', 'injection:write',
        'llm:config'
      ],
      'Manager': [
        'products:read', 'products:write',
        'scheduling:read', 'scheduling:write',
        'injection:read'
      ],
      'User': [
        'products:read',
        'scheduling:read'
      ]
    };

    return rolePermissions[role] || rolePermissions['User'];
  }

  // Método para lidar com mudanças de estado de autenticação
  async handleAuthStateChange(event, session) {
    try {
      console.log('🔄 AuthService: Mudança de estado de autenticação:', event);
      
      if (event === 'SIGNED_IN' && session) {
        this.currentUser = session.user;
        this.currentSession = session;
        
        // Salva dados do usuário no chrome.storage.local
        await this.saveUserData(session.user);
        await this.saveToken(session.access_token);
        
        console.log('✅ AuthService: Usuário autenticado:', session.user.email);
      } else if (event === 'SIGNED_OUT') {
        await this.clearSession();
        console.log('✅ AuthService: Usuário desconectado');
      } else if (event === 'TOKEN_REFRESHED' && session) {
        this.currentSession = session;
        await this.saveToken(session.access_token);
        console.log('✅ AuthService: Token atualizado');
      }
      
      // Notifica listeners sobre a mudança
      this.notifyAuthStateChange(event, session);
    } catch (error) {
      console.error('❌ AuthService: Erro ao lidar com mudança de estado:', error);
    }
  }

  // Método para notificar listeners sobre mudanças de estado
  notifyAuthStateChange(event, session) {
    this.authStateListeners.forEach(listener => {
      try {
        listener(event, session);
      } catch (error) {
        console.error('❌ AuthService: Erro ao notificar listener:', error);
      }
    });
  }

  // Método para adicionar listener de mudança de estado
  onAuthStateChange(callback) {
    this.authStateListeners.push(callback);
    
    // Retorna função para remover o listener
    return () => {
      const index = this.authStateListeners.indexOf(callback);
      if (index > -1) {
        this.authStateListeners.splice(index, 1);
      }
    };
  }

  // Verifica se o usuário está autenticado
  async verifyAuthentication() {
    try {
      // Primeiro tenta carregar sessão do storage
      const storedSession = await this.loadSessionFromPersistentStorage();
      
      if (storedSession && storedSession.session) {
        // Verifica se o config foi inicializado
        if (!this.config) {
          await this.loadConfig();
        }
        
        // Cria cliente Supabase para verificar sessão
        const supabaseClient = await this.config.createSupabaseClient();
        
        // Verifica se a sessão ainda é válida
        const { data: { session }, error } = await supabaseClient.auth.getSession();
        
        if (session && !error) {
          // Sessão válida, atualiza dados locais
          this.token = session.access_token;
          this.user = session.user;
          this.userEmail = session.user.email;
          this.userProfile = this.extractUserRole(session.user) || 'Manager';
          this.isAuthenticated = true;
          return true;
        } else {
          // Sessão inválida, limpa dados
          await this.clearSession();
          return false;
        }
      }
      
      return false;
    } catch (error) {
      console.error('❌ Erro ao verificar autenticação:', error);
      await this.clearSession();
      return false;
    }
  }

  // Método para obter a sessão atual
  async getSession() {
    try {
      // Se já temos uma sessão em memória, retorna ela
      if (this.currentSession) {
        return this.currentSession;
      }

      // Verifica se o config foi inicializado
      if (!this.config) {
        await this.loadConfig();
      }

      // Cria cliente Supabase se não existir
      if (!this.supabaseClient) {
        this.supabaseClient = await this.config.createSupabaseClient();
      }

      // Obtém a sessão atual do Supabase
      const { data: { session }, error } = await this.supabaseClient.auth.getSession();
      
      if (error) {
        console.error('❌ Erro ao obter sessão:', error);
        return null;
      }

      // Atualiza a sessão em memória
      this.currentSession = session;
      
      if (session) {
        this.token = session.access_token;
        this.user = session.user;
        this.userEmail = session.user.email;
        this.userProfile = this.extractUserRole(session.user) || 'Manager';
        this.isAuthenticated = true;
      }

      return session;
    } catch (error) {
      console.error('❌ Erro ao obter sessão:', error);
      return null;
    }
  }

  // Método para fazer login (alias para signInWithPassword)
  async signIn(email, password) {
    return await this.signInWithPassword(email, password);
  }

  // Método para fazer logout
  async signOut() {
    try {
      if (!this.supabaseClient) {
        throw new Error('AuthService não foi inicializado');
      }

      const { error } = await this.supabaseClient.auth.signOut();
      
      if (error) {
        console.error('❌ AuthService: Erro no logout:', error);
        throw error;
      }

      // Limpa dados locais
      await this.clearSession();
      
      console.log('✅ AuthService: Logout realizado com sucesso');
    } catch (error) {
      console.error('❌ AuthService: Erro ao fazer logout:', error);
      throw error;
    }
  }

  // Faz logout do usuário
  async logout() {
    try {
      console.log('🔓 Fazendo logout...');
      
      // Verifica se o config foi inicializado
      if (!this.config) {
        await this.loadConfig();
      }
      
      // Logout usando o SDK oficial do Supabase
      if (this.token) {
        const supabaseClient = await this.config.createSupabaseClient();
        const { error } = await supabaseClient.auth.signOut();
        
        if (error) {
          console.warn('⚠️ Aviso durante logout no Supabase:', error.message);
        }
      }

      // Limpa dados locais independentemente do resultado do servidor
      await this.clearSession();
      this.token = null;
      this.user = null;
      this.userEmail = null;
      this.userProfile = null;
      this.permissions = null;
      this.isAuthenticated = false;
      console.log('✅ Logout realizado com sucesso');
    } catch (error) {
      console.error('❌ Erro durante logout:', error);
      // Limpa dados locais mesmo em caso de erro
      await this.clearSession();
      this.token = null;
      this.user = null;
      this.userEmail = null;
      this.userProfile = null;
      this.permissions = null;
      this.isAuthenticated = false;
      console.log('✅ Logout realizado com sucesso (local)');
    }
  }

  // Armazena sessão usando armazenamento persistente
  async storeSession(sessionData) {
    try {
      if (this.storage) {
        await this.storage.saveUserSession(sessionData);
      } else {
        // Fallback para chrome.storage
        await this.storeToken(sessionData.token);
        await this.storeUserData(sessionData.user);
      }
    } catch (error) {
      console.error('❌ Erro ao armazenar sessão:', error);
    }
  }

  // Recupera sessão armazenada
  async getStoredSession() {
    try {
      if (this.storage) {
        return await this.storage.getUserSession();
      } else {
        // Fallback para chrome.storage
        const token = await this.getStoredToken();
        if (token) {
          return { token, user: null, environment: null };
        }
        return null;
      }
    } catch (error) {
      console.error('❌ Erro ao recuperar sessão:', error);
      return null;
    }
  }

  // Altera ambiente de execução (removido - não aplicável para Supabase)
  // Esta funcionalidade foi removida pois não é necessária com Supabase

  // Retorna headers de autenticação para requisições
  getAuthHeaders() {
    if (!this.token) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  // Salva sessão no armazenamento persistente
  async saveSessionToPersistentStorage(sessionData) {
    try {
      if (typeof persistentStorage !== 'undefined' && persistentStorage) {
        await persistentStorage.saveUserSession(sessionData);
        console.log('✅ Sessão salva no armazenamento persistente');
      }
    } catch (error) {
      console.error('❌ Erro ao salvar sessão:', error);
    }
  }

  // Carrega sessão do armazenamento persistente
  async loadSessionFromPersistentStorage() {
    try {
      if (typeof persistentStorage !== 'undefined' && persistentStorage) {
        const session = await persistentStorage.getUserSession();
        
        if (session && session.token) {
          console.log('✅ Sessão carregada do armazenamento persistente');
          return session; // Retorna a sessão completa para verificação
        }
      }
      
      return null;
    } catch (error) {
      console.error('❌ Erro ao carregar sessão:', error);
      return null;
    }
  }

  // Limpa sessão armazenada
  async clearSession() {
    try {
      this.token = null;
      this.user = null;
      this.userEmail = null;
      this.userProfile = null;
      this.permissions = null;
      this.isAuthenticated = false;

      // Limpa do armazenamento persistente
      if (typeof persistentStorage !== 'undefined' && persistentStorage) {
        await persistentStorage.clearUserSession();
      }

      // Limpa também do chrome.storage.local
      await this.clearToken();
      await this.clearUserData();

      console.log('✅ Sessão limpa com sucesso');
    } catch (error) {
      console.error('❌ Erro ao limpar sessão:', error);
    }
  }

  // Recupera token armazenado
  async getStoredToken() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['auth_token'], (result) => {
        resolve(result.auth_token || null);
      });
    });
  }

  // Limpa token armazenado
  async clearToken() {
    return new Promise((resolve) => {
      chrome.storage.local.remove(['auth_token'], resolve);
    });
  }

  // Armazena dados do usuário
  async storeUserData(userData) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ user_data: userData }, resolve);
    });
  }

  // Alias para storeUserData para compatibilidade
  async saveUserData(userData) {
    return await this.storeUserData(userData);
  }

  // Salva token no storage
  async saveToken(token) {
    return new Promise((resolve) => {
      chrome.storage.local.set({ auth_token: token }, resolve);
    });
  }

  // Limpa dados do usuário
  async clearUserData() {
    return new Promise((resolve) => {
      chrome.storage.local.remove(['user_data'], resolve);
    });
  }

  // Verifica se o usuário tem uma permissão específica
  hasPermission(permission) {
    if (!this.permissions || !Array.isArray(this.permissions)) {
      return false;
    }
    return this.permissions.includes(permission);
  }

  // Métodos de conveniência para verificar permissões específicas
  canAccessProducts() {
    return this.hasPermission('products:read') || this.hasPermission('products:write');
  }

  canAccessScheduling() {
    return this.hasPermission('scheduling:read') || this.hasPermission('scheduling:write');
  }

  canAccessInjection() {
    return this.hasPermission('injection:read') || this.hasPermission('injection:write');
  }

  canAccessLlmConfig() {
    return this.hasPermission('llm:config');
  }

  // Retorna headers de autenticação para requisições
  getAuthHeaders() {
    if (!this.token) {
      return {};
    }
    
    return {
      'Authorization': `Bearer ${this.token}`,
      'Content-Type': 'application/json'
    };
  }

  // Método auxiliar para fazer requisições autenticadas
  async authenticatedFetch(url, options = {}) {
    const headers = {
      ...this.getAuthHeaders(),
      ...options.headers
    };

    const response = await fetch(url, {
      ...options,
      headers
    });

    // Se não autorizado, limpa a sessão
    if (response.status === 401) {
      await this.clearSession();
      throw new Error('Sessão expirada');
    }

    return response;
  }
}

// Controller de autenticação (versão global)
class AuthController {
  constructor() {
    this.authService = new AuthService();
    this.listeners = [];
  }

  async initialize() {
    try {
      await this.authService.initialize();
      console.log('✅ AuthController inicializado');
    } catch (error) {
      console.error('❌ Erro ao inicializar AuthController:', error);
      throw error;
    }
  }

  async isAuthenticated() {
    return this.authService.isAuthenticated;
  }

  async getCurrentUser() {
    return this.authService.user;
  }

  async getUserPermissions() {
    return this.authService.permissions;
  }

  async login(email, password, env = 'prod') {
    try {
      const result = await this.authService.login(email, password, env);
      
      // Notifica listeners sobre mudança de estado
      this.notifyAuthStateChange({
        isAuthenticated: result.success,
        user: result.success ? result.user : null
      });

      return result;
    } catch (error) {
      console.error('Erro no login:', error);
      return { success: false, error: error.message };
    }
  }

  async logout() {
    try {
      const result = await this.authService.logout();
      
      // Notifica listeners sobre mudança de estado
      this.notifyAuthStateChange({
        isAuthenticated: false,
        user: null
      });

      return result;
    } catch (error) {
      console.error('Erro no logout:', error);
      return { success: false, error: error.message };
    }
  }

  addAuthStateListener(callback) {
    this.listeners.push(callback);
  }

  removeAuthStateListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  notifyAuthStateChange(authState) {
    this.listeners.forEach(callback => {
      try {
        callback(authState);
      } catch (error) {
        console.error('Erro ao notificar listener:', error);
      }
    });
  }

  cleanup() {
    this.listeners = [];
  }
}

// Instância global
let authService;

// Inicializa authService de forma segura
try {
  authService = new AuthService();
  
  // Disponibiliza no contexto global
  if (typeof window !== 'undefined') {
    window.authService = authService;
  }
  if (typeof self !== 'undefined') {
    self.authService = authService;
  }
  
  // Inicializa automaticamente
  authService.initialize();
} catch (error) {
  console.error('❌ Erro ao criar AuthService:', error);
}

// Cria instância global do AuthController apenas se não existir
if (typeof AuthController === 'undefined') {
  try {
    const AuthControllerInstance = new AuthControllerClass();
    
    // Disponibiliza no contexto global
    if (typeof window !== 'undefined') {
      window.AuthController = AuthControllerInstance;
    }
    if (typeof self !== 'undefined') {
      self.AuthController = AuthControllerInstance;
    }
    
    // Inicializa após o authService estar pronto
    if (authService) {
      AuthControllerInstance.initialize();
    }
  } catch (error) {
    console.error('❌ Erro ao criar AuthController:', error);
  }
}