/**
 * Serviço para comunicação com Supabase usando o SDK oficial
 */
export class SupabaseService {
  constructor(supabaseClient) {
    this.client = supabaseClient;
    this.schema = 'products_promobell_schema';
  }

  /**
   * Obtém o cliente Supabase
   * @returns {Object}
   */
  getClient() {
    return this.client;
  }

  /**
   * Define o schema padrão para as operações
   * @param {string} schema 
   */
  setSchema(schema) {
    this.schema = schema;
  }

  /**
   * Realiza consulta SELECT usando o SDK do Supabase
   * @param {string} table 
   * @param {Object} options 
   * @returns {Promise<any>}
   */
  async select(table, options = {}) {
    try {
      let query = this.client
        .from(table)
        .select(options.select || '*');

      // Aplicar filtros
      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (typeof value === 'object' && value.operator) {
            switch (value.operator) {
              case 'eq':
                query = query.eq(key, value.value);
                break;
              case 'neq':
                query = query.neq(key, value.value);
                break;
              case 'gt':
                query = query.gt(key, value.value);
                break;
              case 'gte':
                query = query.gte(key, value.value);
                break;
              case 'lt':
                query = query.lt(key, value.value);
                break;
              case 'lte':
                query = query.lte(key, value.value);
                break;
              case 'like':
                query = query.like(key, value.value);
                break;
              case 'ilike':
                query = query.ilike(key, value.value);
                break;
              case 'in':
                query = query.in(key, value.value);
                break;
              case 'is':
                query = query.is(key, value.value);
                break;
            }
          } else {
            query = query.eq(key, value);
          }
        });
      }

      // Aplicar ordenação
      if (options.order) {
        const [column, direction] = options.order.split('.');
        query = query.order(column, { ascending: direction === 'asc' });
      }

      // Aplicar limite
      if (options.limit) {
        query = query.limit(parseInt(options.limit));
      }

      // Aplicar range
      if (options.range) {
        const [from, to] = options.range;
        query = query.range(from, to);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Erro na consulta: ${error.message}`);
      }

      return data;
    } catch (error) {
      throw new Error(`Falha na consulta SELECT: ${error.message}`);
    }
  }

  /**
   * Realiza inserção usando o SDK do Supabase
   * @param {string} table 
   * @param {Object|Array} data 
   * @param {Object} options 
   * @returns {Promise<any>}
   */
  async insert(table, data, options = {}) {
    try {
      let query = this.client
        .from(table)
        .insert(data);

      if (options.select) {
        query = query.select(options.select);
      }

      const { data: result, error } = await query;

      if (error) {
        throw new Error(`Erro na inserção: ${error.message}`);
      }

      return result;
    } catch (error) {
      throw new Error(`Falha na inserção: ${error.message}`);
    }
  }

  /**
   * Realiza atualização usando o SDK do Supabase
   * @param {string} table 
   * @param {Object} data 
   * @param {Object} filters 
   * @param {Object} options 
   * @returns {Promise<any>}
   */
  async update(table, data, filters = {}, options = {}) {
    try {
      let query = this.client
        .from(table)
        .update(data);

      // Aplicar filtros
      Object.entries(filters).forEach(([key, value]) => {
        if (typeof value === 'object' && value.operator) {
          switch (value.operator) {
            case 'eq':
              query = query.eq(key, value.value);
              break;
            case 'neq':
              query = query.neq(key, value.value);
              break;
            case 'gt':
              query = query.gt(key, value.value);
              break;
            case 'gte':
              query = query.gte(key, value.value);
              break;
            case 'lt':
              query = query.lt(key, value.value);
              break;
            case 'lte':
              query = query.lte(key, value.value);
              break;
            case 'like':
              query = query.like(key, value.value);
              break;
            case 'ilike':
              query = query.ilike(key, value.value);
              break;
            case 'in':
              query = query.in(key, value.value);
              break;
            case 'is':
              query = query.is(key, value.value);
              break;
          }
        } else {
          query = query.eq(key, value);
        }
      });

      if (options.select) {
        query = query.select(options.select);
      }

      const { data: result, error } = await query;

      if (error) {
        throw new Error(`Erro na atualização: ${error.message}`);
      }

      return result;
    } catch (error) {
      throw new Error(`Falha na atualização: ${error.message}`);
    }
  }

  /**
   * Realiza exclusão usando o SDK do Supabase
   * @param {string} table 
   * @param {Object} filters 
   * @param {Object} options 
   * @returns {Promise<any>}
   */
  async delete(table, filters = {}, options = {}) {
    try {
      let query = this.client
        .from(table)
        .delete();

      // Aplicar filtros
      Object.entries(filters).forEach(([key, value]) => {
        if (typeof value === 'object' && value.operator) {
          switch (value.operator) {
            case 'eq':
              query = query.eq(key, value.value);
              break;
            case 'neq':
              query = query.neq(key, value.value);
              break;
            case 'gt':
              query = query.gt(key, value.value);
              break;
            case 'gte':
              query = query.gte(key, value.value);
              break;
            case 'lt':
              query = query.lt(key, value.value);
              break;
            case 'lte':
              query = query.lte(key, value.value);
              break;
            case 'like':
              query = query.like(key, value.value);
              break;
            case 'ilike':
              query = query.ilike(key, value.value);
              break;
            case 'in':
              query = query.in(key, value.value);
              break;
            case 'is':
              query = query.is(key, value.value);
              break;
          }
        } else {
          query = query.eq(key, value);
        }
      });

      if (options.select) {
        query = query.select(options.select);
      }

      const { data: result, error } = await query;

      if (error) {
        throw new Error(`Erro na exclusão: ${error.message}`);
      }

      return result;
    } catch (error) {
      throw new Error(`Falha na exclusão: ${error.message}`);
    }
  }

  /**
   * Obtém contagem de registros usando o SDK do Supabase
   * @param {string} table 
   * @param {Object} filters 
   * @returns {Promise<number>}
   */
  async getCount(table, filters = {}) {
    try {
      let query = this.client
        .from(table)
        .select('*', { count: 'exact', head: true });

      // Aplicar filtros
      Object.entries(filters).forEach(([key, value]) => {
        if (typeof value === 'object' && value.operator) {
          switch (value.operator) {
            case 'eq':
              query = query.eq(key, value.value);
              break;
            case 'neq':
              query = query.neq(key, value.value);
              break;
            case 'gt':
              query = query.gt(key, value.value);
              break;
            case 'gte':
              query = query.gte(key, value.value);
              break;
            case 'lt':
              query = query.lt(key, value.value);
              break;
            case 'lte':
              query = query.lte(key, value.value);
              break;
            case 'like':
              query = query.like(key, value.value);
              break;
            case 'ilike':
              query = query.ilike(key, value.value);
              break;
            case 'in':
              query = query.in(key, value.value);
              break;
            case 'is':
              query = query.is(key, value.value);
              break;
          }
        } else {
          query = query.eq(key, value);
        }
      });

      const { count, error } = await query;

      if (error) {
        throw new Error(`Erro na contagem: ${error.message}`);
      }

      return count || 0;
    } catch (error) {
      throw new Error(`Falha na contagem: ${error.message}`);
    }
  }

  /**
   * Realiza login via Supabase Auth
   * @param {string} email 
   * @param {string} password 
   * @returns {Promise<any>}
   */
  async login(email, password) {
    const response = await fetch(`${this.supabaseUrl}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': this.supabaseAnonKey
      },
      body: JSON.stringify({
        email,
        password
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error_description || 'Erro no login');
    }

    return await response.json();
  }

  /**
   * Realiza logout via Supabase Auth
   * @returns {Promise<void>}
   */
  async logout() {
    const response = await fetch(`${this.supabaseUrl}/auth/v1/logout`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'apikey': this.supabaseAnonKey
      }
    });

    if (!response.ok) {
      throw new Error('Erro no logout');
    }

    this.token = null;
  }

  /**
   * Obtém usuário atual via Supabase Auth
   * @returns {Promise<any>}
   */
  async getCurrentUser() {
    if (!this.token) {
      return null;
    }

    const response = await fetch(`${this.supabaseUrl}/auth/v1/user`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.token}`,
        'apikey': this.supabaseAnonKey
      }
    });

    if (!response.ok) {
      return null;
    }

    const user = await response.json();
    return {
      user,
      session: {
        access_token: this.token,
        refresh_token: null,
        expires_at: null
      }
    };
  }
}