# Resumo da Extensão Promobell WhatsApp Automation

## Visão Geral
A **Promobell WhatsApp Automation** é uma extensão do Chrome desenvolvida para automatizar o envio de produtos agendados através do WhatsApp Web. A extensão monitora produtos armazenados no Supabase e os envia automaticamente nos horários programados.

## Funcionalidades Principais

### 1. Automação de Mensagens WhatsApp
- **Envio Automático**: Envia produtos agendados automaticamente no WhatsApp Web
- **Monitoramento em Tempo Real**: Monitora continuamente produtos no banco Supabase
- **Formatação Inteligente**: Formata mensagens com preços, cupons e informações de frete
- **Suporte a Imagens**: Inclui imagens dos produtos nas mensagens
- **Funcionamento em Background**: Opera continuamente através de Service Worker

### 2. Gerenciamento de Produtos
- **Agendamento**: Permite agendar produtos para envio em horários específicos
- **Filtragem**: Filtra produtos por status de envio e data de agendamento
- **Controle de Estado**: Marca produtos como enviados após processamento
- **Contadores**: Exibe contagem de produtos pendentes e enviados

### 3. Interface de Usuário
- **Painel Lateral**: Interface integrada ao Chrome via Side Panel API
- **Lista de Produtos**: Visualização de produtos agendados
- **Toggle de Automação**: Controle para ativar/desativar automação
- **Status em Tempo Real**: Indicadores visuais do status da automação
- **Botão de Limpeza**: Opção para limpar agendamentos

### 4. Sistema de Autenticação
- **Autenticação Supabase**: Sistema de login integrado com Supabase
- **Gerenciamento de Sessão**: Controle automático de tokens e refresh
- **Permissões**: Sistema de controle de acesso baseado em perfis
- **Armazenamento Seguro**: Tokens armazenados localmente de forma segura

### 5. Configuração de Ambiente
- **Múltiplos Ambientes**: Suporte a desenvolvimento e produção
- **Configuração Flexível**: Chaves e URLs configuráveis por ambiente
- **Alternância Dinâmica**: Possibilidade de alternar entre ambientes

## Arquitetura Técnica

### Estrutura de Pastas
```
src/
├── domain/
│   ├── entities/          # Entidades de domínio (Product, User)
│   └── usecases/          # Casos de uso (AuthUseCase, ProductUseCase)
├── infrastructure/
│   ├── repositories/      # Repositórios (AuthRepository, ProductRepository)
│   └── services/          # Serviços (SupabaseService)
└── presentation/
    ├── controllers/       # Controladores (AppController, AuthController, ProductController)
    └── views/            # Views (ProductsPage, SidebarPage)
```

### Componentes Principais

#### 1. Service Worker (background.js)
- **WhatsAppAutomation**: Classe principal que gerencia toda automação
- **Monitoramento**: Verifica produtos agendados em intervalos regulares
- **Processamento**: Processa produtos individuais e coordena envio
- **Integração**: Comunica com WhatsApp Web via Content Script

#### 2. Content Script (content-whatsapp.js)
- **WhatsAppController**: Controla interações com WhatsApp Web
- **Envio de Mensagens**: Executa o envio real das mensagens
- **Monitoramento de Interface**: Aguarda carregamento e monitora mudanças
- **Navegação**: Garante que está no canal correto antes do envio

#### 3. Repositórios
- **ProductRepository**: Gerencia operações CRUD de produtos no Supabase
- **AuthRepository**: Gerencia autenticação e sessões de usuário

#### 4. Casos de Uso
- **ProductUseCase**: Lógica de negócio para produtos (filtragem, agendamento)
- **AuthUseCase**: Lógica de negócio para autenticação

#### 5. Entidades
- **Product**: Representa um produto com propriedades como título, preço, data de agendamento
- **User**: Representa um usuário com informações de autenticação

### Fluxo de Funcionamento

1. **Inicialização**:
   - Service Worker inicializa WhatsAppAutomation
   - Carrega configurações do ambiente
   - Estabelece conexão com Supabase
   - Inicia monitoramento em tempo real

2. **Agendamento de Produto**:
   - Usuário agenda produto através da interface
   - Produto é salvo no Supabase com data/hora específica
   - Sistema atualiza contadores na interface

3. **Processamento Automático**:
   - Sistema verifica produtos agendados periodicamente
   - Identifica produtos que devem ser enviados no momento atual
   - Processa cada produto individualmente

4. **Envio no WhatsApp**:
   - Verifica se WhatsApp Web está aberto
   - Navega para o canal correto
   - Formata mensagem com informações do produto
   - Envia mensagem e marca produto como enviado

### Tecnologias Utilizadas

- **Chrome Extension APIs**: Manifest V3, Service Workers, Content Scripts, Side Panel
- **Supabase**: Banco de dados PostgreSQL, autenticação, real-time subscriptions
- **JavaScript ES6+**: Classes, async/await, modules
- **Chrome Storage API**: Armazenamento local de configurações
- **WhatsApp Web Integration**: Automação via DOM manipulation

### Configuração e Instalação

1. **Pré-requisitos**:
   - Chrome browser
   - Conta Supabase configurada
   - Projeto Supabase com tabelas de produtos

2. **Configuração**:
   - Definir nome e ID do canal WhatsApp
   - Configurar ambiente (desenvolvimento/produção)
   - Ativar automação através do toggle

3. **Permissões Necessárias**:
   - `storage`: Para armazenamento local
   - `activeTab`: Para interação com abas
   - `alarms`: Para agendamentos
   - `background`: Para Service Worker
   - `tabs`: Para gerenciamento de abas
   - `sidePanel`: Para interface lateral
   - `host_permissions`: Para WhatsApp Web e Supabase

### Segurança e Boas Práticas

- **Tokens Seguros**: Armazenamento seguro de tokens de autenticação
- **Validação**: Validação de dados antes do processamento
- **Tratamento de Erros**: Tratamento robusto de erros e exceções
- **Cleanup**: Limpeza adequada de recursos e listeners
- **Permissões Mínimas**: Apenas permissões necessárias são solicitadas

### Casos de Uso Típicos

1. **E-commerce**: Envio automático de promoções e ofertas especiais
2. **Marketing**: Campanhas programadas de produtos
3. **Vendas**: Notificações automáticas de produtos em oferta
4. **Atendimento**: Envio programado de informações de produtos

### Limitações e Considerações

- **Dependência do WhatsApp Web**: Requer que o WhatsApp Web esteja aberto
- **Conexão Internet**: Necessita conexão estável para funcionamento
- **Políticas WhatsApp**: Deve respeitar termos de uso do WhatsApp
- **Rate Limiting**: Implementar controles para evitar spam

### Extensibilidade

A arquitetura modular permite fácil extensão com:
- Novos tipos de mensagem
- Integração com outras plataformas
- Funcionalidades de relatório
- Sistemas de notificação adicionais
- Integração com outros bancos de dados

## Conclusão

A extensão Promobell WhatsApp Automation oferece uma solução completa e robusta para automação de envio de produtos via WhatsApp, com arquitetura bem estruturada, interface intuitiva e funcionalidades avançadas de agendamento e monitoramento.