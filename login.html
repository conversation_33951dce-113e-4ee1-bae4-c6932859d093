<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Login - Promobell WhatsApp Automation</title>
  <script src="supabase.js"></script>
  <script src="env-loader.js"></script>
  <script src="config.js"></script>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      width: 100%;
      min-height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      display: flex;
      flex-direction: column;
    }

    .header {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logo {
      width: 50px;
      height: 50px;
      margin: 0 auto 15px;
      background: #25D366;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
    }

    .title {
      color: white;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 5px;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
    }

    .content {
      flex: 1;
      padding: 30px 20px;
      background: white;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .login-form {
      max-width: 300px;
      margin: 0 auto;
      width: 100%;
    }

    .form-title {
      text-align: center;
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 30px;
      color: #333;
    }

    .form-group {
      margin-bottom: 20px;
    }

    .form-label {
      display: block;
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #333;
    }

    .form-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      font-size: 16px;
      transition: border-color 0.2s;
    }

    .form-input:focus {
      outline: none;
      border-color: #667eea;
    }

    .form-input.error {
      border-color: #dc3545;
    }

    .btn {
      width: 100%;
      padding: 14px;
      border: none;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 15px;
    }

    .btn-primary {
      background: #667eea;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #5a6fd8;
      transform: translateY(-1px);
    }

    .btn-primary:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }

    .btn-secondary {
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .btn-secondary:hover {
      background: rgba(255, 255, 255, 0.3);
    }

    .message {
      padding: 12px;
      border-radius: 8px;
      margin: 15px 0;
      font-size: 14px;
      text-align: center;
    }

    .error-message {
      background: rgba(220, 53, 69, 0.1);
      color: #dc3545;
      border: 1px solid rgba(220, 53, 69, 0.3);
    }

    .success-message {
      background: rgba(40, 167, 69, 0.1);
      color: #28a745;
      border: 1px solid rgba(40, 167, 69, 0.3);
    }

    .checkbox-container {
      display: flex;
      align-items: center;
      cursor: pointer;
      color: white;
      font-size: 14px;
    }

    .checkbox-container input[type="checkbox"] {
      margin-right: 8px;
    }

    .checkmark {
      margin-left: 8px;
    }

    .loading {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #ffffff;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s ease-in-out infinite;
      margin-right: 8px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .hidden {
      display: none;
    }
  </style>
</head>

<body>
  <div class="header">
    <div class="logo">📱</div>
    <div class="title">Promobell WhatsApp Automation</div>
    <div class="subtitle">Faça login para continuar</div>
  </div>

  <div class="content">
    <div class="login-form">
      <h2 class="form-title">Entrar</h2>
      
      <div id="errorMessage" class="error-message hidden"></div>
      <div id="successMessage" class="success-message hidden"></div>

      <form id="loginForm">
        <div class="form-group">
          <label for="email" class="form-label">E-mail</label>
          <input type="email" id="email" class="form-input" required>
        </div>

        <div class="form-group">
          <label for="password" class="form-label">Senha</label>
          <input type="password" id="password" class="form-input" required>
        </div>

        <div class="form-group">
          <label class="checkbox-container">
            <input type="checkbox" id="rememberEmail">
            <span class="checkmark"></span>
            Lembrar e-mail
          </label>
        </div>

        <div id="errorMessage" class="message error-message" style="display: none;"></div>
        <div id="successMessage" class="message success-message" style="display: none;"></div>

        <button type="submit" id="loginBtn" class="btn btn-primary">
          Entrar
        </button>
      </form>
    </div>
  </div>

  <script src="auth.js"></script>
  <script>
    class LoginManager {
      constructor() {
        this.initializeElements();
        this.loadSavedData();
        this.bindEvents();
      }

      initializeElements() {
        this.loginForm = document.getElementById('loginForm');
        this.emailInput = document.getElementById('email');
        this.passwordInput = document.getElementById('password');
        this.rememberEmailCheckbox = document.getElementById('rememberEmail');
        this.loginBtn = document.getElementById('loginBtn');
        this.errorMessage = document.getElementById('errorMessage');
        this.successMessage = document.getElementById('successMessage');
      }

      async loadSavedData() {
        // Carrega dados salvos do usuário
        const result = await chrome.storage.local.get(['savedEmail', 'rememberEmail']);

        if (result.rememberEmail && result.savedEmail) {
          this.emailInput.value = result.savedEmail;
          this.rememberEmailCheckbox.checked = true;
        }
      }

      bindEvents() {
        this.loginForm.addEventListener('submit', this.handleLogin.bind(this));
      }

      async handleLogin(e) {
        e.preventDefault();
        
        const email = this.emailInput.value.trim();
        const password = this.passwordInput.value.trim();

        if (!email || !password) {
          this.showError('Por favor, preencha todos os campos');
          return;
        }

        this.setLoading(true);
        this.hideMessages();

        try {
          // Reinicializa o AuthService
          await authService.initialize();

          // Tenta fazer login
          const result = await authService.login(email, password);

          if (result.success) {
            // Salva e-mail se solicitado
            if (this.rememberEmailCheckbox.checked) {
              await chrome.storage.local.set({ 
                savedEmail: email, 
                rememberEmail: true 
              });
            } else {
              await chrome.storage.local.remove(['savedEmail', 'rememberEmail']);
            }

            this.showSuccess('Login realizado com sucesso!');
            
            // Redireciona para a página principal após 1 segundo
            setTimeout(() => {
              window.location.href = 'sidebar.html';
            }, 1000);
          } else {
            this.showError(result.error || 'Erro no login');
          }
        } catch (error) {
          console.error('Erro no login:', error);
          this.showError('Erro de autenticação. Verifique suas credenciais.');
        } finally {
          this.setLoading(false);
        }
      }

      setLoading(loading) {
        if (loading) {
          this.loginBtn.disabled = true;
          this.loginBtn.innerHTML = '<span class="loading"></span> Entrando...';
        } else {
          this.loginBtn.disabled = false;
          this.loginBtn.innerHTML = 'Entrar';
        }
      }

      showError(message) {
        this.hideMessages();
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
      }

      showSuccess(message) {
        this.hideMessages();
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
      }

      hideMessages() {
        this.errorMessage.style.display = 'none';
        this.successMessage.style.display = 'none';
      }
    }

    // Verifica se já está logado
    authService.initialize().then(() => {
      if (authService.isAuthenticated) {
        window.location.href = 'sidebar.html';
      } else {
        // Inicializa o gerenciador de login
        new LoginManager();
      }
    });
  </script>
</body>

</html>