/**
 * View responsável pela interface do sidebar
 */
class SidebarPage {
  constructor() {
    this.appController = null;
    this.authController = null;
    this.productController = null;
    this.scheduledProducts = [];
    this.automationEnabled = false;

    // Aguarda o DOM estar pronto antes de inicializar
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.initializeElements();
        this.bindEvents();
        this.initialize();
      });
    } else {
      this.initializeElements();
      this.bindEvents();
      this.initialize();
    }
  }

  /**
   * Inicializa elementos do DOM
   */
  initializeElements() {
    // Telas principais
    this.loginScreen = document.getElementById('loginScreen');
    this.authSection = document.getElementById('authSection');
    this.mainContent = document.getElementById('mainContent');
    this.loading = document.getElementById('loading');
    this.error = document.getElementById('error');
    this.success = document.getElementById('success');

    // Elementos da tela de login
    this.email = document.getElementById('email');
    this.password = document.getElementById('password');
    this.loginSubmitBtn = document.getElementById('loginSubmitBtn');

    // Elementos da seção autenticada
    this.userEmail = document.getElementById('userEmail');
    this.userProfile = document.getElementById('userProfile');
    this.userProfileHeader = document.getElementById('userProfileHeader');
    this.logoutBtn = document.getElementById('logoutBtn');
    this.environmentSection = document.getElementById('environmentSection');
    this.serverUrl = document.getElementById('serverUrl');
    this.saveConfigBtn = document.getElementById('saveConfigBtn');

    // Elementos da seção de produtos
    this.channelName = document.getElementById('channelName');
    this.queueList = document.getElementById('queueList');
    this.queueEmpty = document.getElementById('queueEmpty');

    // Elementos de controle de automação
    this.automationToggle = document.getElementById('automationToggle');
    this.pendingCount = document.getElementById('pendingCount');
    this.sentCount = document.getElementById('sentCount');
    this.statusCard = document.getElementById('automationSection');
    this.statusIndicator = document.getElementById('statusIndicator');
    this.statusTitle = document.getElementById('statusTitle');
    this.statusText = document.getElementById('statusText');
  }

  /**
   * Vincula eventos aos elementos
   */
  bindEvents() {
    // Eventos de login
    if (this.loginSubmitBtn) {
      this.loginSubmitBtn.addEventListener('click', (e) => {
        e.preventDefault();
        this.handleLogin();
      });
    }

    // Eventos de logout
    if (this.logoutBtn) {
      this.logoutBtn.addEventListener('click', () => this.handleLogout());
    }

    // Eventos de configuração
    if (this.saveConfigBtn) {
      this.saveConfigBtn.addEventListener('click', () => this.handleSaveConfig());
    }

    // Eventos de automação
    if (this.automationToggle) {
      this.automationToggle.addEventListener('change', (e) => {
        this.handleAutomationToggle(e.target.checked);
      });
    }

    // Eventos de formulário de login
    if (this.email && this.password) {
      [this.email, this.password].forEach(input => {
        input.addEventListener('keypress', (e) => {
          if (e.key === 'Enter') {
            e.preventDefault();
            this.handleLogin();
          }
        });
      });
    }
  }

  /**
   * Inicializa a página
   */
  async initialize() {
    try {
      this.showLoading('Inicializando...');

      // Obtém o AppController do background
      const response = await chrome.runtime.sendMessage({ action: 'getAppController' });
      if (response && response.success) {
        console.log('✅ SidebarPage: Resposta do background recebida:', response);

        // Usa os dados serializáveis em vez de métodos
        this.appController = response.appController;
        this.authController = response.authService;
        this.productController = response.productController;

        console.log('✅ SidebarPage: Controllers configurados');
        console.log('- AppController ready:', this.appController?.isReady);
        console.log('- AuthController available:', !!this.authController);
        console.log('- ProductController available:', !!this.productController);
      } else {
        console.error('❌ SidebarPage: Falha ao obter AppController:', response?.error);
        this.showError('Erro ao conectar com o serviço de autenticação');
        return;
      }

      await this.checkAuthStatus();
      await this.loadEnvironmentConfig();
      await this.loadScheduledProducts();
      await this.updateAutomationStatus();
    } catch (error) {
      console.error('Erro ao inicializar:', error);
      this.showError('Erro ao inicializar a aplicação');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Manipula mudanças de estado de autenticação
   */
  handleAuthStateChange(event, session) {
    console.log('🔄 SidebarPage: Mudança de estado de autenticação:', event);

    if (event === 'SIGNED_IN' && session) {
      this.showAuthenticatedView(session.user);
      this.loadUserData();
      this.showSuccess('Login realizado com sucesso!');
    } else if (event === 'SIGNED_OUT') {
      this.showLoginView();
      this.clearLocalData();
      this.showSuccess('Logout realizado com sucesso!');
    } else if (event === 'TOKEN_REFRESHED') {
      console.log('✅ SidebarPage: Token atualizado');
    }
  }

  /**
   * Verifica o status de autenticação atual
   */
  async checkAuthStatus() {
    try {
      console.log('🔍 SidebarPage: Verificando status de autenticação...');

      // Verifica sessão via background script
      const response = await chrome.runtime.sendMessage({ action: 'auth_getSession' });

      if (response && response.success) {
        if (response.session && response.user) {
          console.log('✅ SidebarPage: Usuário autenticado:', response.user.email);
          this.showAuthenticatedView(response.user);
          await this.loadUserData();
        } else {
          console.log('ℹ️ SidebarPage: Usuário não autenticado');
          this.showLoginView();
        }
      } else {
        console.error('❌ SidebarPage: Erro ao verificar sessão:', response?.error);
        this.showLoginView();
      }
    } catch (error) {
      console.error('❌ SidebarPage: Erro ao verificar status de autenticação:', error);
      this.showLoginView();
    }
  }

  /**
   * Obtém dados do usuário armazenados
   */
  async getStoredUserData() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['userData'], (result) => {
        resolve(result.userData || null);
      });
    });
  }

  /**
   * Manipula o processo de login
   */
  async handleLogin() {
    try {
      this.showLoading('Fazendo login...');

      const email = this.email.value.trim();
      const password = this.password.value.trim();

      if (!email || !password) {
        this.showError('Por favor, preencha todos os campos');
        return;
      }

      console.log('🔐 SidebarPage: Tentando fazer login para:', email);

      // Faz login via background script
      const response = await chrome.runtime.sendMessage({
        action: 'auth_signIn',
        email: email,
        password: password
      });

      if (response && response.success) {
        console.log('✅ SidebarPage: Login realizado com sucesso');
        this.showAuthenticatedView(response.user);
        await this.loadUserData();
        this.showSuccess('Login realizado com sucesso!');

        // Limpa os campos de login
        this.email.value = '';
        this.password.value = '';
      } else {
        console.error('❌ SidebarPage: Erro no login:', response?.error);
        this.showError(response?.error || 'Erro ao fazer login');
      }
    } catch (error) {
      console.error('❌ SidebarPage: Erro no processo de login:', error);
      this.showError('Erro interno ao fazer login');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Carrega dados do usuário
   */
  async loadUserData() {
    try {
      // Carrega configurações do ambiente
      await this.loadEnvironmentConfig();

      // Carrega produtos agendados
      await this.loadScheduledProducts();

      // Atualiza status da automação
      await this.updateAutomationStatus();
    } catch (error) {
      console.error('Erro ao carregar dados do usuário:', error);
    }
  }

  /**
   * Carrega configurações do ambiente
   */
  async loadEnvironmentConfig() {
    try {
      const { config = {}, channelName, automationEnabled, environment } = await chrome.storage.local.get(['config', 'channelName', 'automationEnabled', 'environment']);

      const resolvedEnvironment = environment || config.serverUrl || 'dev';
      const resolvedChannelName = channelName || config.channelName || '';
      const resolvedAutomation = typeof automationEnabled === 'boolean'
        ? automationEnabled
        : Boolean(config.automationEnabled);

      if (this.serverUrl) this.serverUrl.value = resolvedEnvironment;
      if (this.channelName && resolvedChannelName) this.channelName.value = resolvedChannelName;
      if (this.automationToggle) this.automationToggle.checked = resolvedAutomation;

      this.automationEnabled = resolvedAutomation;
      await this.updateAutomationStatus({ automationEnabled: resolvedAutomation });
    } catch (error) {
      console.error('Erro ao carregar configuração:', error);
    }
  }

  /**
   * Obtém configuração armazenada
   */
  async getStoredConfig() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['config'], (result) => {
        resolve(result.config || null);
      });
    });
  }

  /**
   * Manipula salvamento de configuração
   */
  async handleSaveConfig() {
    const environment = this.serverUrl?.value || 'dev';
    const channelName = this.channelName?.value?.trim();
    const automationEnabled = Boolean(this.automationToggle?.checked);

    if (!channelName) {
      this.showError('Informe o nome ou ID do canal');
      return;
    }

    try {
      this.showLoading('Salvando configuração...');

      const config = {
        serverUrl: environment,
        channelName,
        automationEnabled
      };

      await this.saveConfig(config);

      await chrome.storage.local.set({
        channelName,
        environment,
        automationEnabled
      });

      this.automationEnabled = automationEnabled;

      if (typeof window !== 'undefined' && window.config) {
        try {
          await window.config.setEnvironment(environment);
          await window.config.loadEnvironment();
          console.log('✅ Ambiente atualizado no config global');
        } catch (error) {
          console.warn('⚠️ Não foi possível atualizar o config global:', error);
        }
      }

      try {
        const automationResponse = await chrome.runtime.sendMessage({
          action: 'setAutomationState',
          enabled: automationEnabled
        });

        if (!automationResponse?.success) {
          console.warn('⚠️ Falha ao sincronizar estado da automação ao salvar:', automationResponse?.error);
        }
      } catch (error) {
        console.warn('⚠️ Não foi possível sincronizar a automação ao salvar:', error);
      }

      try {
        await chrome.runtime.sendMessage({
          action: 'updateChannelName',
          channelName
        });
        console.log('✅ Background notificado sobre mudança de configuração');
      } catch (error) {
        console.warn('⚠️ Não foi possível notificar o background:', error);
      }

      try {
        await chrome.runtime.sendMessage({
          action: 'config_updated',
          config
        });
      } catch (error) {
        console.warn('⚠️ Não foi possível informar a atualização de configuração ao background:', error);
      }

      await this.updateAutomationStatus({ automationEnabled });
      this.showSuccess('Configuração salva com sucesso!');
    } catch (error) {
      console.error('Erro ao salvar configuração:', error);
      this.showError('Erro ao salvar configuração: ' + (error.message || 'Erro desconhecido'));
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Salva configuração
   */
  async saveConfig(config) {
    return new Promise((resolve, reject) => {
      try {
        chrome.storage.local.set({ config }, () => {
          if (chrome.runtime.lastError) {
            console.error('❌ Erro do Chrome Storage:', chrome.runtime.lastError);
            reject(new Error(chrome.runtime.lastError.message || 'Erro ao salvar no storage'));
          } else {
            console.log('✅ Configuração salva no storage local');
            resolve();
          }
        });
      } catch (error) {
        console.error('❌ Erro ao acessar Chrome Storage:', error);
        reject(error);
      }
    });
  }

  /**
   * Carrega produtos agendados
   */
  async loadScheduledProducts() {
    try {
      console.log('🔄 Carregando produtos agendados...');

      const localProducts = await this.getScheduledProducts();
      this.scheduledProducts = Array.isArray(localProducts)
        ? localProducts.map((product) => this.normalizeScheduledProduct(product)).filter(Boolean)
        : [];

      this.sortScheduledProducts();
      this.updateScheduledProductsList();

      try {
        const response = await chrome.runtime.sendMessage({
          action: 'getScheduledProducts'
        });

        if (response && response.success && Array.isArray(response.products)) {
          const normalized = response.products
            .map((product) => this.normalizeScheduledProduct(product))
            .filter(Boolean);

          if (normalized.length) {
            this.scheduledProducts = normalized;
            this.sortScheduledProducts();
            this.updateScheduledProductsList();

            await chrome.storage.local.set({
              scheduledProducts: normalized
            });
          }
        } else if (response && !response.success) {
          console.warn('⚠️ Não foi possível carregar produtos do Supabase:', response.error);
        }
      } catch (error) {
        console.warn('⚠️ Erro ao carregar produtos do Supabase:', error);
      }

      await this.updateAutomationStatus();
    } catch (error) {
      console.error('❌ Erro ao carregar produtos agendados:', error);
      this.showError('Erro ao carregar produtos agendados');
    }
  }

  /**
   * Obtém produtos agendados
   */
  async getScheduledProducts() {
    return new Promise((resolve) => {
      chrome.storage.local.get(['scheduledProducts'], (result) => {
        resolve(result.scheduledProducts || []);
      });
    });
  }

  /**
   * Atualiza lista de produtos agendados
   */
  updateScheduledProductsList() {
    if (!this.queueList) return;

    if (!Array.isArray(this.scheduledProducts) || this.scheduledProducts.length === 0) {
      if (this.queueEmpty) {
        this.queueList.innerHTML = '';
        this.queueList.appendChild(this.queueEmpty);
        this.queueEmpty.style.display = 'block';
      } else {
        this.queueList.innerHTML = '<p class="no-products">Nenhum produto agendado</p>';
      }
      return;
    }

    if (this.queueEmpty) {
      this.queueEmpty.style.display = 'none';
    }

    const html = this.scheduledProducts.map((product) => {
      const title = this.escapeHtml(product?.titulo || product?.name || product?.title || 'Produto sem nome');
      const priceValue = typeof product?.preco_atual === 'number'
        ? product.preco_atual
        : Number(String(product?.preco_atual || product?.price || '').replace(/[^0-9.,-]/g, '').replace(',', '.'));
      const hasValidPrice = Number.isFinite(priceValue);
      const priceText = hasValidPrice ? MessageFormatter.formatPrice(priceValue) : 'Preço indisponível';

      const scheduledDate = product?.date_schedule || product?.scheduledTime || product?.scheduleDate;
      const formattedDate = scheduledDate ? DateUtils.formatDate(scheduledDate) : 'Sem data definida';

      const isSent = Boolean(product?.enviado_whatsapp);
      const statusLabel = isSent ? 'Enviado' : 'Pendente';
      const statusStyles = isSent
        ? 'background:#d4edda;color:#155724;'
        : 'background:#fff3cd;color:#856404;';

      return '<div class="queue-item" style="padding:12px;border:1px solid #e9ecef;border-radius:8px;margin-bottom:10px;background:#fff;">' +
        '<div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:6px;">' +
        '<span style="font-weight:600;color:#333;">' + title + '</span>' +
        '<span style="font-size:12px;padding:2px 8px;border-radius:999px;' + statusStyles + '">' + statusLabel + '</span>' +
        '</div>' +
        '<div style="font-size:12px;color:#555;margin-bottom:4px;">Data: ' + formattedDate + '</div>' +
        '<div style="font-size:12px;color:#555;">Preço: ' + priceText + '</div>' +
        '</div>';
    }).join('');

    this.queueList.innerHTML = html;

    if (this.queueEmpty) {
      this.queueList.appendChild(this.queueEmpty);
    }
  }

  sortScheduledProducts() {
    if (!Array.isArray(this.scheduledProducts)) return;

    this.scheduledProducts.sort((a, b) => {
      const getTime = (value) => {
        if (!value) return Number.MAX_SAFE_INTEGER;
        const parsed = new Date(value);
        return Number.isNaN(parsed.getTime()) ? Number.MAX_SAFE_INTEGER : parsed.getTime();
      };

      const timeA = getTime(a?.scheduledTime || a?.date_schedule);
      const timeB = getTime(b?.scheduledTime || b?.date_schedule);
      return timeA - timeB;
    });
  }

  normalizeScheduledProduct(product) {
    if (!product || typeof product !== 'object') {
      return null;
    }

    const normalized = { ...product };

    const fallbackId = typeof crypto !== 'undefined' && crypto.randomUUID
      ? crypto.randomUUID()
      : Math.random().toString(36).slice(2);

    normalized.id = product.id || product.product_id || product.uuid || fallbackId;
    normalized.titulo = product.titulo || product.name || product.title || product.descricao || 'Produto sem nome';

    const rawPrice = product.preco_atual ?? product.price ?? product.preco ?? product.valor ?? null;
    if (typeof rawPrice === 'number') {
      normalized.preco_atual = rawPrice;
    } else if (typeof rawPrice === 'string') {
      const parsed = Number(rawPrice.replace(/[^0-9.,-]/g, '').replace(',', '.'));
      normalized.preco_atual = Number.isFinite(parsed) ? parsed : null;
    } else {
      normalized.preco_atual = null;
    }

    const scheduleSource = product.date_schedule || product.scheduledTime || product.scheduleDate || product.schedule_date || product.date || null;
    if (scheduleSource) {
      const parsedDate = new Date(scheduleSource);
      normalized.date_schedule = Number.isNaN(parsedDate.getTime()) ? scheduleSource : parsedDate.toISOString();
    } else {
      normalized.date_schedule = null;
    }

    normalized.scheduledTime = normalized.date_schedule || scheduleSource || null;
    normalized.enviado_whatsapp = Boolean(product.enviado_whatsapp ?? product.sent ?? false);

    return normalized;
  }

  escapeHtml(value) {
    if (typeof value !== 'string') return '';
    return value
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  /**
     * Remove produto agendado
     */
  async removeScheduledProduct(productId) {
    try {
      this.scheduledProducts = this.scheduledProducts.filter(p => p.id !== productId);
      this.sortScheduledProducts();
      await this.saveScheduledProducts();
      this.updateScheduledProductsList();
      this.updateAutomationStatus();
      this.showSuccess('Produto removido da agenda');
    } catch (error) {
      console.error('Erro ao remover produto:', error);
      this.showError('Erro ao remover produto');
    }
  }

  /**
   * Salva produtos agendados
   */
  async saveScheduledProducts() {
    return new Promise((resolve, reject) => {
      chrome.storage.local.set({ scheduledProducts: this.scheduledProducts }, () => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
        } else {
          resolve();
        }
      });
    });
  }

  /**
   * Manipula limpeza da agenda
   */
  async handleClearSchedule() {
    if (confirm('Tem certeza que deseja limpar toda a agenda?')) {
      try {
        this.scheduledProducts = [];
        await this.saveScheduledProducts();
        this.updateScheduledProductsList();
        this.updateAutomationStatus();
        this.showSuccess('Agenda limpa com sucesso');
      } catch (error) {
        console.error('Erro ao limpar agenda:', error);
        this.showError('Erro ao limpar agenda');
      }
    }
  }

  /**
   * Atualiza status da automação
   */
  async updateAutomationStatus({ automationEnabled } = {}) {
    try {
      const pending = Array.isArray(this.scheduledProducts)
        ? this.scheduledProducts.filter((product) => !product?.enviado_whatsapp).length
        : 0;
      const sent = Array.isArray(this.scheduledProducts)
        ? this.scheduledProducts.filter((product) => product?.enviado_whatsapp).length
        : 0;

      if (this.pendingCount) this.pendingCount.textContent = String(pending);
      if (this.sentCount) this.sentCount.textContent = String(sent);

      const resolvedAutomation = typeof automationEnabled === 'boolean'
        ? automationEnabled
        : (typeof this.automationEnabled === 'boolean'
          ? this.automationEnabled
          : Boolean(this.automationToggle?.checked));

      this.automationEnabled = resolvedAutomation;

      if (this.automationToggle) {
        this.automationToggle.checked = resolvedAutomation;
      }

      if (this.statusCard && this.statusIndicator && this.statusTitle && this.statusText) {
        if (resolvedAutomation && pending > 0) {
          this.statusCard.classList.remove('inactive');
          this.statusIndicator.classList.remove('inactive');
          this.statusTitle.textContent = 'Automação Ativa';
          this.statusText.textContent = 'Monitorando produtos agendados';
        } else if (resolvedAutomation) {
          this.statusCard.classList.remove('inactive');
          this.statusIndicator.classList.remove('inactive');
          this.statusTitle.textContent = 'Automação Habilitada';
          this.statusText.textContent = 'Aguardando novos produtos';
        } else {
          this.statusCard.classList.add('inactive');
          this.statusIndicator.classList.add('inactive');
          this.statusTitle.textContent = 'Automação Inativa';
          this.statusText.textContent = 'Clique no toggle para ativar';
        }
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
    }
  }

  /**
   * Manipula toggle da automação
   */
  async handleAutomationToggle(enabled) {
    try {
      if (enabled && (!Array.isArray(this.scheduledProducts) || this.scheduledProducts.length === 0)) {
        this.showError('Não há produtos agendados para automação');
        if (this.automationToggle) this.automationToggle.checked = false;
        return;
      }

      this.showLoading(enabled ? 'Ativando automação...' : 'Desativando automação...');

      const response = await chrome.runtime.sendMessage({
        action: 'setAutomationState',
        enabled
      });

      if (!response || !response.success) {
        throw new Error(response?.error || 'Não foi possível atualizar a automação');
      }

      this.automationEnabled = enabled;
      await chrome.storage.local.set({ automationEnabled: enabled });
      await this.updateAutomationStatus({ automationEnabled: enabled });

      this.showSuccess(`Automação ${enabled ? 'ativada' : 'desativada'}`);
    } catch (error) {
      console.error('Erro ao alterar automação:', error);
      this.showError(error.message || 'Erro ao alterar automação');
      if (this.automationToggle) {
        this.automationToggle.checked = !enabled;
      }
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Manipula atualização de produtos
   */
  async handleRefreshProducts() {
    try {
      this.showLoading('Atualizando produtos...');

      if (this.productController) {
        await this.productController.refreshProducts();
        this.showSuccess('Produtos atualizados');
      } else {
        this.showError('Controlador de produtos não disponível');
      }
    } catch (error) {
      console.error('Erro ao atualizar produtos:', error);
      this.showError('Erro ao atualizar produtos');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Mostra a view de login
   */
  showLoginView() {
    if (this.loginScreen) this.loginScreen.style.display = 'block';
    if (this.authSection) this.authSection.style.display = 'none';
    if (this.mainContent) this.mainContent.style.display = 'none';
  }

  /**
   * Mostra a view autenticada
   */
  showAuthenticatedView(user) {
    if (this.loginScreen) this.loginScreen.style.display = 'none';
    if (this.authSection) this.authSection.style.display = 'block';
    if (this.mainContent) this.mainContent.style.display = 'block';

    // Atualiza informações do usuário
    if (this.userEmail) this.userEmail.textContent = user.email || 'Usuário';
    if (this.userProfile) this.userProfile.textContent = user.email || 'Usuário';
    if (this.userProfileHeader) this.userProfileHeader.textContent = user.email || 'Usuário';
  }

  /**
   * Mostra loading
   */
  showLoading(message = 'Carregando...') {
    if (this.loading) {
      this.loading.textContent = message;
      this.loading.style.display = 'block';
    }
  }

  /**
   * Esconde loading
   */
  hideLoading() {
    if (this.loading) {
      this.loading.style.display = 'none';
    }
  }

  /**
   * Mostra mensagem de erro
   */
  showError(message) {
    if (this.error) {
      this.error.textContent = message;
      this.error.style.display = 'block';
      setTimeout(() => {
        if (this.error) this.error.style.display = 'none';
      }, 5000);
    }
    console.error(message);
  }

  /**
   * Mostra mensagem de sucesso
   */
  showSuccess(message) {
    if (this.success) {
      this.success.textContent = message;
      this.success.style.display = 'block';
      setTimeout(() => {
        if (this.success) this.success.style.display = 'none';
      }, 3000);
    }
  }

  /**
   * Manipula logout
   */
  async handleLogout() {
    try {
      this.showLoading('Fazendo logout...');

      console.log('🚪 SidebarPage: Iniciando processo de logout');

      // Faz logout via background script
      const response = await chrome.runtime.sendMessage({ action: 'auth_signOut' });

      if (response && response.success) {
        console.log('✅ SidebarPage: Logout realizado com sucesso');
        this.showLoginView();
        await this.clearLocalData();
        this.showSuccess('Logout realizado com sucesso!');
      } else {
        console.error('❌ SidebarPage: Erro no logout:', response?.error);
        // Mesmo com erro, limpa os dados locais e mostra tela de login
        this.showLoginView();
        await this.clearLocalData();
        this.showError('Erro no logout, mas dados locais foram limpos');
      }
    } catch (error) {
      console.error('❌ SidebarPage: Erro no processo de logout:', error);
      // Em caso de erro, força a limpeza local
      this.showLoginView();
      await this.clearLocalData();
      this.showError('Erro no logout, mas dados locais foram limpos');
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Limpa dados locais
   */
  async clearLocalData() {
    return new Promise((resolve) => {
      chrome.storage.local.clear(() => {
        resolve();
      });
    });
  }

  /**
   * Cleanup da view
   */
  cleanup() {
    if (this.appController) {
      this.appController.cleanup();
    }
  }
}

// Torna a classe disponível globalmente
window.SidebarPage = SidebarPage;

// Instância global para acesso aos métodos
let sidebarPage = null;

// Inicializa quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
  sidebarPage = new SidebarPage();
  window.sidebarPage = sidebarPage;
});
