<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 20px;
      font-family: Arial, sans-serif;
    }
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    .status {
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 15px;
      text-align: center;
    }
    .status.active {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .status.inactive {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .button {
      width: 100%;
      padding: 10px;
      margin: 5px 0;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }
    .button.primary {
      background-color: #007bff;
      color: white;
    }
    .button.secondary {
      background-color: #6c757d;
      color: white;
    }
    .button:hover {
      opacity: 0.8;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>WhatsApp Automation</h3>
  </div>
  
  <div id="status" class="status inactive">
    Automação Inativa
  </div>
  
  <button id="toggleBtn" class="button primary">Ativar Automação</button>
  <button id="openSidebarBtn" class="button secondary">Abrir Painel</button>
  
  <script src="popup-simple.js"></script>
</body>
</html>