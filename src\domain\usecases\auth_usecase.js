import { User } from '../entities/user_entity.js';

/**
 * Use Case para operações de autenticação
 */
export class AuthUseCase {
  constructor(authRepository) {
    this.authRepository = authRepository;
  }

  /**
   * Realiza login do usuário
   * @param {string} email 
   * @param {string} password 
   * @returns {Promise<User>}
   */
  async login(email, password) {
    try {
      const authData = await this.authRepository.login(email, password);

      return new User({
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || authData.user.email,
        token: authData.session.access_token,
        refreshToken: authData.session.refresh_token,
        expiresAt: authData.session.expires_at
      });
    } catch (error) {
      throw new Error(`Erro no login: ${error.message}`);
    }
  }

  /**
   * Realiza logout do usuário
   * @returns {Promise<void>}
   */
  async logout() {
    try {
      await this.authRepository.logout();
    } catch (error) {
      throw new Error(`Erro no logout: ${error.message}`);
    }
  }

  /**
   * Obtém usuário atual
   * @returns {Promise<User|null>}
   */
  async getCurrentUser() {
    try {
      const authData = await this.authRepository.getCurrentUser();

      if (!authData || !authData.user) {
        return null;
      }

      return new User({
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || authData.user.email,
        token: authData.session?.access_token,
        refreshToken: authData.session?.refresh_token,
        expiresAt: authData.session?.expires_at
      });
    } catch (error) {
      console.error('Erro ao obter usuário atual:', error);
      return null;
    }
  }

  /**
   * Verifica se há um usuário autenticado
   * @returns {Promise<boolean>}
   */
  async isAuthenticated() {
    const user = await this.getCurrentUser();
    return user ? user.isAuthenticated() : false;
  }

  /**
   * Atualiza token do usuário
   * @param {string} refreshToken 
   * @returns {Promise<User>}
   */
  async refreshToken(refreshToken) {
    try {
      const authData = await this.authRepository.refreshToken(refreshToken);

      return new User({
        id: authData.user.id,
        email: authData.user.email,
        name: authData.user.user_metadata?.name || authData.user.email,
        token: authData.session.access_token,
        refreshToken: authData.session.refresh_token,
        expiresAt: authData.session.expires_at
      });
    } catch (error) {
      throw new Error(`Erro ao atualizar token: ${error.message}`);
    }
  }
}