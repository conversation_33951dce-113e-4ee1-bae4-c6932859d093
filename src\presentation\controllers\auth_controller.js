/**
 * Controller para gerenciar operações de autenticação
 */
export class AuthController {
  constructor(authUseCase) {
    this.authUseCase = authUseCase;
    this.currentUser = null;
    this.listeners = [];
  }

  /**
   * Adiciona listener para mudanças de estado de autenticação
   * @param {Function} callback 
   */
  addAuthStateListener(callback) {
    this.listeners.push(callback);
  }

  /**
   * Remove listener de mudanças de estado
   * @param {Function} callback 
   */
  removeAuthStateListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  /**
   * Notifica listeners sobre mudanças de estado
   * @param {Object} state 
   */
  notifyListeners(state) {
    this.listeners.forEach(listener => {
      try {
        listener(state);
      } catch (error) {
        console.error('Erro ao notificar listener:', error);
      }
    });
  }

  /**
   * Realiza login do usuário
   * @param {string} email 
   * @param {string} password 
   * @returns {Promise<{success: boolean, user?: Object, error?: string}>}
   */
  async login(email, password) {
    try {
      if (!email || !password) {
        return {
          success: false,
          error: 'Email e senha são obrigatórios'
        };
      }

      const user = await this.authUseCase.login(email, password);
      this.currentUser = user;

      this.notifyListeners({
        isAuthenticated: true,
        user: user.toJSON()
      });

      return {
        success: true,
        user: user.toJSON()
      };
    } catch (error) {
      console.error('Erro no login:', error);
      
      this.notifyListeners({
        isAuthenticated: false,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Realiza logout do usuário
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  async logout() {
    try {
      await this.authUseCase.logout();
      this.currentUser = null;

      this.notifyListeners({
        isAuthenticated: false,
        user: null
      });

      return {
        success: true
      };
    } catch (error) {
      console.error('Erro no logout:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Verifica se usuário está autenticado
   * @returns {Promise<boolean>}
   */
  async isAuthenticated() {
    try {
      return await this.authUseCase.isAuthenticated();
    } catch (error) {
      console.error('Erro ao verificar autenticação:', error);
      return false;
    }
  }

  /**
   * Obtém usuário atual
   * @returns {Promise<Object|null>}
   */
  async getCurrentUser() {
    try {
      if (!this.currentUser) {
        this.currentUser = await this.authUseCase.getCurrentUser();
      }

      return this.currentUser ? this.currentUser.toJSON() : null;
    } catch (error) {
      console.error('Erro ao obter usuário atual:', error);
      return null;
    }
  }

  /**
   * Inicializa o controller verificando estado de autenticação
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      const user = await this.authUseCase.getCurrentUser();
      
      if (user && user.isAuthenticated()) {
        this.currentUser = user;
        
        this.notifyListeners({
          isAuthenticated: true,
          user: user.toJSON()
        });
      } else {
        this.notifyListeners({
          isAuthenticated: false,
          user: null
        });
      }
    } catch (error) {
      console.error('Erro ao inicializar AuthController:', error);
      
      this.notifyListeners({
        isAuthenticated: false,
        error: error.message
      });
    }
  }

  /**
   * Atualiza token do usuário
   * @param {string} refreshToken 
   * @returns {Promise<{success: boolean, user?: Object, error?: string}>}
   */
  async refreshToken(refreshToken) {
    try {
      const user = await this.authUseCase.refreshToken(refreshToken);
      this.currentUser = user;

      this.notifyListeners({
        isAuthenticated: true,
        user: user.toJSON()
      });

      return {
        success: true,
        user: user.toJSON()
      };
    } catch (error) {
      console.error('Erro ao atualizar token:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }
}