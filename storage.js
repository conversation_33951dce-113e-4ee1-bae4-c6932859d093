// Sistema de armazenamento local persistente para a extensão
class PersistentStorage {
  constructor() {
    this.dbName = 'PromobelExtensionDB';
    this.dbVersion = 1;
    this.storeName = 'userSession';
    this.db = null;
  }

  // Inicializa o banco de dados IndexedDB
  async init() {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => {
        console.error('❌ Erro ao abrir IndexedDB:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ IndexedDB inicializado com sucesso');
        resolve(this.db);
      };

      request.onupgradeneeded = (event) => {
        const db = event.target.result;
        
        // Criar object store se não existir
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          console.log('📦 Object store criado:', this.storeName);
        }
      };
    });
  }

  // Salva dados no armazenamento local
  async setItem(key, value) {
    try {
      if (!this.db) {
        await this.init();
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        
        const data = {
          key: key,
          value: value,
          timestamp: new Date().toISOString()
        };

        const request = store.put(data);

        request.onsuccess = () => {
          console.log(`💾 Dados salvos: ${key}`);
          resolve(true);
        };

        request.onerror = () => {
          console.error('❌ Erro ao salvar dados:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      console.error('❌ Erro no setItem:', error);
      throw error;
    }
  }

  // Recupera dados do armazenamento local
  async getItem(key) {
    try {
      if (!this.db) {
        await this.init();
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.get(key);

        request.onsuccess = () => {
          const result = request.result;
          if (result) {
            console.log(`📖 Dados recuperados: ${key}`);
            resolve(result.value);
          } else {
            console.log(`📭 Nenhum dado encontrado para: ${key}`);
            resolve(null);
          }
        };

        request.onerror = () => {
          console.error('❌ Erro ao recuperar dados:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      console.error('❌ Erro no getItem:', error);
      return null;
    }
  }

  // Remove dados do armazenamento local
  async removeItem(key) {
    try {
      if (!this.db) {
        await this.init();
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.delete(key);

        request.onsuccess = () => {
          console.log(`🗑️ Dados removidos: ${key}`);
          resolve(true);
        };

        request.onerror = () => {
          console.error('❌ Erro ao remover dados:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      console.error('❌ Erro no removeItem:', error);
      throw error;
    }
  }

  // Limpa todos os dados
  async clear() {
    try {
      if (!this.db) {
        await this.init();
      }

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const request = store.clear();

        request.onsuccess = () => {
          console.log('🧹 Todos os dados foram limpos');
          resolve(true);
        };

        request.onerror = () => {
          console.error('❌ Erro ao limpar dados:', request.error);
          reject(request.error);
        };
      });
    } catch (error) {
      console.error('❌ Erro no clear:', error);
      throw error;
    }
  }

  // Salva sessão do usuário
  async saveUserSession(sessionData) {
    try {
      await this.setItem('userSession', {
        token: sessionData.token,
        user: sessionData.user,
        environment: sessionData.environment,
        loginTime: new Date().toISOString(),
        expiresAt: sessionData.expiresAt || null
      });
      console.log('✅ Sessão do usuário salva com sucesso');
      return true;
    } catch (error) {
      console.error('❌ Erro ao salvar sessão:', error);
      return false;
    }
  }

  // Recupera sessão do usuário
  async getUserSession() {
    try {
      const session = await this.getItem('userSession');
      
      if (!session) {
        console.log('📭 Nenhuma sessão encontrada');
        return null;
      }

      // Verificar se a sessão não expirou
      if (session.expiresAt) {
        const now = new Date();
        const expiresAt = new Date(session.expiresAt);
        
        if (now > expiresAt) {
          console.log('⏰ Sessão expirada, removendo...');
          await this.removeItem('userSession');
          return null;
        }
      }

      console.log('✅ Sessão válida recuperada');
      return session;
    } catch (error) {
      console.error('❌ Erro ao recuperar sessão:', error);
      return null;
    }
  }

  // Remove sessão do usuário (logout)
  async clearUserSession() {
    try {
      await this.removeItem('userSession');
      console.log('🚪 Sessão do usuário removida (logout)');
      return true;
    } catch (error) {
      console.error('❌ Erro ao remover sessão:', error);
      return false;
    }
  }

  // Verifica se há uma sessão válida
  async hasValidSession() {
    const session = await this.getUserSession();
    return session !== null;
  }
}

// Instância global do armazenamento
const persistentStorage = new PersistentStorage();

// Exportar para uso em outros arquivos
if (typeof window !== 'undefined') {
  window.persistentStorage = persistentStorage;
}

if (typeof self !== 'undefined') {
  self.persistentStorage = persistentStorage;
}