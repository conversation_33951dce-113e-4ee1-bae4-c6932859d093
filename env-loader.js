/**
 * Carregador de variáveis de ambiente para extensão Chrome
 * Lê as configurações dos arquivos .env.dev e .env.prod
 */
class EnvLoader {
  constructor() {
    this.currentEnv = 'dev'; // 'dev' ou 'prod'
    this.envVars = {};
    this.isLoaded = false;
  }

  /**
   * <PERSON>eg<PERSON> as variáveis de ambiente do arquivo apropriado
   */
  async loadEnv() {
    try {
      console.log('🔄 EnvLoader: Carregando variáveis de ambiente...');
      
      // Determina qual arquivo .env usar
      const envFile = this.currentEnv === 'prod' ? '.env.prod' : '.env.dev';
      
      // Carrega o arquivo .env usando fetch
      const response = await fetch(chrome.runtime.getURL(envFile));
      
      if (!response.ok) {
        throw new Error(`Erro ao carregar ${envFile}: ${response.status}`);
      }
      
      const envContent = await response.text();
      this.envVars = this.parseEnvContent(envContent);
      this.isLoaded = true;
      
      console.log(`✅ EnvLoader: Variáveis carregadas de ${envFile}`);
      console.log('📋 EnvLoader: Variáveis disponíveis:', Object.keys(this.envVars));
      
      return this.envVars;
    } catch (error) {
      console.error('❌ EnvLoader: Erro ao carregar variáveis de ambiente:', error);
      this.isLoaded = false;
      return {};
    }
  }

  /**
   * Faz o parse do conteúdo do arquivo .env
   */
  parseEnvContent(content) {
    const envVars = {};
    
    // Validação do conteúdo
    if (typeof content !== 'string') {
      console.warn('⚠️ EnvLoader: Conteúdo do arquivo .env não é uma string:', typeof content);
      return envVars;
    }
    
    const lines = content.split('\n');
    
    for (const line of lines) {
      // Validação da linha
      if (typeof line !== 'string') {
        console.warn('⚠️ EnvLoader: Linha não é uma string:', typeof line, line);
        continue;
      }
      
      const trimmedLine = line.trim();
      
      // Ignora linhas vazias e comentários
      if (!trimmedLine || trimmedLine.startsWith('#')) {
        continue;
      }
      
      // Procura por padrão KEY=VALUE
      const equalIndex = trimmedLine.indexOf('=');
      if (equalIndex > 0) {
        const key = trimmedLine.substring(0, equalIndex).trim();
        let value = trimmedLine.substring(equalIndex + 1).trim();
        
        // Remove aspas se existirem
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
          value = value.slice(1, -1);
        }
        
        envVars[key] = value;
      }
    }
    
    return envVars;
  }

  /**
   * Obtém uma variável de ambiente
   */
  get(key, defaultValue = null) {
    if (!this.isLoaded) {
      console.warn('⚠️ EnvLoader: Variáveis de ambiente não foram carregadas ainda');
      return defaultValue;
    }
    
    return this.envVars[key] || defaultValue;
  }

  /**
   * Obtém todas as variáveis de ambiente
   */
  getAll() {
    return { ...this.envVars };
  }

  /**
   * Define o ambiente atual (dev ou prod)
   */
  async setEnvironment(env) {
    if (env !== 'dev' && env !== 'prod') {
      throw new Error('Ambiente deve ser "dev" ou "prod"');
    }
    
    console.log(`🔄 EnvLoader: Alterando ambiente para: ${env}`);
    this.currentEnv = env;
    
    // Salva no storage da extensão
    await chrome.storage.local.set({ environment: env });
    
    // Recarrega as variáveis
    await this.loadEnv();
    
    console.log(`✅ EnvLoader: Ambiente alterado para: ${env}`);
  }

  /**
   * Carrega o ambiente salvo no storage
   */
  async loadSavedEnvironment() {
    try {
      const result = await chrome.storage.local.get(['environment']);
      if (result.environment) {
        this.currentEnv = result.environment;
        console.log(`📋 EnvLoader: Ambiente carregado do storage: ${this.currentEnv}`);
      }
    } catch (error) {
      console.error('❌ EnvLoader: Erro ao carregar ambiente do storage:', error);
    }
  }

  /**
   * Obtém o ambiente atual
   */
  getCurrentEnvironment() {
    return this.currentEnv;
  }

  /**
   * Verifica se as variáveis foram carregadas
   */
  isEnvironmentLoaded() {
    return this.isLoaded;
  }

  /**
   * Inicializa o carregador de ambiente
   */
  async initialize() {
    await this.loadSavedEnvironment();
    await this.loadEnv();
    return this.envVars;
  }
}

// Instância global do carregador de ambiente
const envLoader = new EnvLoader();

// Exporta para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { EnvLoader, envLoader };
} else if (typeof window !== 'undefined') {
  window.EnvLoader = EnvLoader;
  window.envLoader = envLoader;
} else if (typeof self !== 'undefined') {
  // Para Service Workers
  self.EnvLoader = EnvLoader;
  self.envLoader = envLoader;
}