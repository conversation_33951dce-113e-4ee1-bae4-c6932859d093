/**
 * Controller para gerenciar operações com produtos
 */
export class ProductController {
  constructor(productUseCase) {
    this.productUseCase = productUseCase;
    this.products = [];
    this.listeners = [];
  }

  /**
   * Adiciona listener para mudanças nos produtos
   * @param {Function} callback 
   */
  addProductsListener(callback) {
    this.listeners.push(callback);
  }

  /**
   * Remove listener de mudanças nos produtos
   * @param {Function} callback 
   */
  removeProductsListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  /**
   * Notifica listeners sobre mudanças nos produtos
   * @param {Object} data 
   */
  notifyListeners(data) {
    this.listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Erro ao notificar listener:', error);
      }
    });
  }

  /**
   * Obtém produtos agendados
   * @returns {Promise<{success: boolean, products?: Object[], count?: number, error?: string}>}
   */
  async getScheduledProducts() {
    try {
      const products = await this.productUseCase.getScheduledProducts();
      const count = await this.productUseCase.getScheduledProductsCount();
      
      // Ordena produtos por data de agendamento
      const sortedProducts = this.productUseCase.sortProductsByScheduleDate(products, 'asc');
      
      this.products = sortedProducts;

      const result = {
        success: true,
        products: sortedProducts.map(product => product.toJSON()),
        count
      };

      this.notifyListeners({
        type: 'products_updated',
        data: result
      });

      return result;
    } catch (error) {
      console.error('Erro ao buscar produtos agendados:', error);
      
      const result = {
        success: false,
        error: error.message,
        products: [],
        count: 0
      };

      this.notifyListeners({
        type: 'products_error',
        data: result
      });

      return result;
    }
  }

  /**
   * Obtém apenas a contagem de produtos agendados
   * @returns {Promise<{success: boolean, count?: number, error?: string}>}
   */
  async getScheduledProductsCount() {
    try {
      const count = await this.productUseCase.getScheduledProductsCount();
      
      const result = {
        success: true,
        count
      };

      this.notifyListeners({
        type: 'count_updated',
        data: result
      });

      return result;
    } catch (error) {
      console.error('Erro ao buscar contagem de produtos:', error);
      
      return {
        success: false,
        error: error.message,
        count: 0
      };
    }
  }

  /**
   * Marca produto como enviado
   * @param {string} productId 
   * @returns {Promise<{success: boolean, product?: Object, error?: string}>}
   */
  async markProductAsSent(productId) {
    try {
      if (!productId) {
        return {
          success: false,
          error: 'ID do produto é obrigatório'
        };
      }

      const updatedProduct = await this.productUseCase.markProductAsSent(productId);
      
      // Atualiza lista local
      const productIndex = this.products.findIndex(p => p.id === productId);
      if (productIndex !== -1) {
        this.products[productIndex] = updatedProduct;
      }

      const result = {
        success: true,
        product: updatedProduct.toJSON()
      };

      this.notifyListeners({
        type: 'product_sent',
        data: result
      });

      // Atualiza lista completa após marcar como enviado
      await this.getScheduledProducts();

      return result;
    } catch (error) {
      console.error('Erro ao marcar produto como enviado:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Obtém produto por ID
   * @param {string} productId 
   * @returns {Promise<{success: boolean, product?: Object, error?: string}>}
   */
  async getProductById(productId) {
    try {
      if (!productId) {
        return {
          success: false,
          error: 'ID do produto é obrigatório'
        };
      }

      const product = await this.productUseCase.getProductById(productId);
      
      if (!product) {
        return {
          success: false,
          error: 'Produto não encontrado'
        };
      }

      return {
        success: true,
        product: product.toJSON()
      };
    } catch (error) {
      console.error('Erro ao buscar produto:', error);
      
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Filtra produtos por status de envio
   * @param {boolean} sent 
   * @returns {Object[]}
   */
  filterProductsBySentStatus(sent = false) {
    const filteredProducts = this.productUseCase.filterProductsBySentStatus(this.products, sent);
    return filteredProducts.map(product => product.toJSON());
  }

  /**
   * Filtra produtos agendados para o futuro
   * @returns {Object[]}
   */
  getFutureScheduledProducts() {
    const futureProducts = this.productUseCase.filterFutureScheduledProducts(this.products);
    return futureProducts.map(product => product.toJSON());
  }

  /**
   * Inicializa o controller carregando produtos
   * @returns {Promise<void>}
   */
  async initialize() {
    try {
      await this.getScheduledProducts();
    } catch (error) {
      console.error('Erro ao inicializar ProductController:', error);
      
      this.notifyListeners({
        type: 'initialization_error',
        data: {
          success: false,
          error: error.message
        }
      });
    }
  }

  /**
   * Atualiza produtos periodicamente
   * @param {number} intervalMs 
   * @returns {number} intervalId
   */
  startPeriodicUpdate(intervalMs = 30000) {
    return setInterval(async () => {
      try {
        await this.getScheduledProducts();
      } catch (error) {
        console.error('Erro na atualização periódica:', error);
      }
    }, intervalMs);
  }

  /**
   * Para atualização periódica
   * @param {number} intervalId 
   */
  stopPeriodicUpdate(intervalId) {
    if (intervalId) {
      clearInterval(intervalId);
    }
  }
}