# Promobell WhatsApp Automation

Extensão do Chrome para envio automático de produtos agendados no WhatsApp Web.

## Funcionalidades

- ✅ Monitoramento automático de produtos agendados no Supabase
- ✅ Envio automático de mensagens no WhatsApp Web
- ✅ Formatação de mensagens baseada no modelo Flutter
- ✅ Suporte a imagens de produtos
- ✅ Alternância entre ambientes de desenvolvimento e produção
- ✅ Interface de configuração intuitiva
- ✅ Funcionamento em background (não precisa manter aba ativa)

## Instalação

1. Clone ou baixe este repositório
2. Abra o Chrome e vá para `chrome://extensions/`
3. Ative o "Modo do desenvolvedor"
4. Clique em "Carregar sem compactação"
5. Selecione a pasta da extensão

## Configuração

### 🔧 Configuração

1. **Instalar a Extensão**:
   - Abra o Chrome e vá para `chrome://extensions/`
   - Ative o "Modo do desenvolvedor"
   - Clique em "Carregar sem compactação"
   - Selecione a pasta da extensão

2. **Configurar Canal**:
   - Clique no ícone da extensão na barra de ferramentas
   - **Nome do Canal**: Digite o nome exato do canal (ex: "Promobell {Dev}")
   - **ID do Canal**: Ou cole o link/ID do canal (ex: `https://whatsapp.com/channel/0029VbApX2gBadmdU9AB2H3V` ou apenas `0029VbApX2gBadmdU9AB2H3V`)
   - Selecione o ambiente (Dev/Prod)
   - Ative a automação com o toggle

3. **Configurar Banco de Dados**:
   - Certifique-se que os arquivos `.env.dev` e `.env.prod` estão configurados
   - A tabela `scheduled_products` deve existir no Supabase

### 1. Ambiente
- **Desenvolvimento**: Usa banco de dados de teste
- **Produção**: Usa banco de dados de produção

### 2. Canal do WhatsApp
- Configure o nome exato do canal onde as mensagens serão enviadas
- Padrão: "Promobell {Dev}"

### 3. Automação
- Use o toggle para ativar/desativar o monitoramento automático
- A extensão verifica produtos agendados a cada 30 segundos

## Como Usar

1. **Instale a extensão** seguindo os passos acima
2. **Abra o WhatsApp Web** em uma aba do navegador
3. **Configure a extensão** clicando no ícone na barra de ferramentas
4. **Ative a automação** usando o toggle na interface
5. **Aguarde**: A extensão monitorará automaticamente os produtos agendados

## Estrutura do Projeto

```
├── manifest.json           # Configuração da extensão
├── background.js           # Service worker principal
├── content-whatsapp.js     # Script para interação com WhatsApp Web
├── popup.html             # Interface do popup
├── popup.js               # Lógica do popup
├── config.js              # Configurações de ambiente
├── utils.js               # Utilitários e formatadores
├── images/                # Ícones da extensão
└── README.md              # Esta documentação
```

## Formato da Mensagem

A extensão gera mensagens no seguinte formato:

```
[Título do Produto]

Compre na/no [Plataforma]

De ~R$ [Preço Antigo]~
Por *R$ [Preço Atual]*

🚚 Frete: [Grátis/Consulte no site]
🎟️ Cupom: [Código do cupom/Indisponível]

[Link do produto]
```

## Banco de Dados

A extensão consulta a tabela `scheduled_products` no Supabase com os seguintes campos:

- `id`: ID único do produto
- `titulo`: Título do produto
- `plataforma`: Nome da plataforma (Mercado Livre, Amazon, etc.)
- `preco_atual`: Preço atual do produto
- `preco_antigo`: Preço anterior (para mostrar desconto)
- `url_imagem`: URL da imagem do produto
- `frete`: Boolean indicando se o frete é grátis
- `cupom`: Código do cupom (se disponível)
- `date_schedule`: Data/hora agendada para envio
- `disparar_whatsapp`: Flag indicando se já foi enviado

## Ambientes

### Desenvolvimento
- URL: `https://izokqmnpvvyowbnsjekv.supabase.co`
- Usado para testes e desenvolvimento

### Produção
- URL: `https://vzffiuzzlouvlfgyebic.supabase.co`
- Usado para envios reais

## Troubleshooting

### A extensão não está enviando mensagens
1. Verifique se o WhatsApp Web está aberto e logado
2. Confirme se o nome do canal está correto
3. Verifique se há produtos agendados para o horário atual
4. Teste a conexão com o Supabase no popup

### Mensagens não estão sendo formatadas corretamente
1. Verifique se todos os campos obrigatórios estão preenchidos no banco
2. Confirme se os preços estão em formato numérico
3. Verifique se as URLs das imagens são válidas

### A automação não está funcionando
1. Verifique se a automação está ativada no popup
2. Confirme se a extensão tem as permissões necessárias
3. Verifique o console do navegador para erros

## Desenvolvimento

Para modificar a extensão:

1. Faça as alterações nos arquivos
2. Vá para `chrome://extensions/`
3. Clique no botão "Recarregar" da extensão
4. Teste as modificações

## Segurança

- As chaves de API estão configuradas nos arquivos de ambiente
- A extensão só funciona em `web.whatsapp.com`
- Todas as comunicações são feitas via HTTPS

## Suporte

Para dúvidas ou problemas, consulte os logs do console do navegador ou entre em contato com a equipe de desenvolvimento.