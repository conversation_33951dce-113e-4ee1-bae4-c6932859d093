import { AppController } from '../controllers/app_controller.js';

/**
 * View responsável pela interface do sidebar
 */
export class SidebarPage {
  constructor() {
    this.appController = null;
    this.authController = null;
    this.productController = null;
    this.scheduledProducts = [];

    // Aguarda o DOM estar pronto antes de inicializar
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.initializeElements();
        this.bindEvents();
        this.initialize();
      });
    } else {
      this.initializeElements();
      this.bindEvents();
      this.initialize();
    }
  }

  /**
   * Inicializa elementos do DOM
   */
  initializeElements() {
    // Telas principais
    this.loginScreen = document.getElementById('loginScreen');
    this.authSection = document.getElementById('authSection');
    this.mainContent = document.getElementById('mainContent');
    this.loading = document.getElementById('loading');
    this.error = document.getElementById('error');
    this.success = document.getElementById('success');

    // Elementos da tela de login
    this.email = document.getElementById('email');
    this.password = document.getElementById('password');
    this.loginSubmitBtn = document.getElementById('loginSubmitBtn');

    // Elementos da seção autenticada
    this.userEmail = document.getElementById('userEmail');
    this.userProfile = document.getElementById('userProfile');
    this.userProfileHeader = document.getElementById('userProfileHeader');
    this.logoutBtn = document.getElementById('logoutBtn');
    this.environmentSection = document.getElementById('environmentSection');
    this.serverUrl = document.getElementById('serverUrl');

    // Elementos da fila de agendamentos
    this.queueList = document.getElementById('queueList');
    this.queueEmpty = document.getElementById('queueEmpty');
    this.pendingCount = document.getElementById('pendingCount');
  }

  /**
   * Vincula eventos aos elementos
   */
  bindEvents() {
    if (this.loginSubmitBtn) {
      this.loginSubmitBtn.addEventListener('click', this.handleLogin.bind(this));
    }
    if (this.logoutBtn) {
      this.logoutBtn.addEventListener('click', this.handleLogout.bind(this));
    }
    if (this.serverUrl) {
      this.serverUrl.addEventListener('change', this.handleEnvironmentChange.bind(this));
    }

    // Enter key no formulário de login
    if (this.password) {
      this.password.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
          this.handleLogin();
        }
      });
    }
  }

  /**
   * Inicializa a aplicação
   */
  async initialize() {
    try {
      // Aguarda um pouco para garantir que o DOM esteja completamente carregado
      await new Promise(resolve => setTimeout(resolve, 100));

      // Inicializa o controller principal
      this.appController = AppController.create();
      await this.appController.initialize();

      // Obtém controllers específicos
      this.authController = this.appController.getAuthController();
      this.productController = this.appController.getProductController();

      // Configura listeners
      this.setupControllerListeners();

      // Verifica estado de autenticação
      const isAuthenticated = await this.authController.isAuthenticated();

      if (isAuthenticated) {
        this.showAuthenticatedState();
      } else {
        this.showLoginScreen();
      }
    } catch (error) {
      console.error('Erro na inicialização:', error);
      this.showError('Erro ao inicializar aplicação');
      this.showLoginScreen();
    }
  }

  /**
   * Configura listeners dos controllers
   */
  setupControllerListeners() {
    // Listener para mudanças de autenticação
    this.authController.addAuthStateListener((authState) => {
      if (authState.isAuthenticated) {
        this.showAuthenticatedState();
      } else {
        this.showLoginScreen();
      }
    });

    // Listener para atualizações de produtos
    this.productController.addListener((event) => {
      if (event.type === 'products_updated') {
        this.updateProductsDisplay(event.data.products, event.data.count);
      } else if (event.type === 'products_cleared') {
        this.clearProductsDisplay();
      }
    });
  }

  /**
   * Exibe/oculta loading
   */
  showLoading(show) {
    if (this.loading) {
      this.loading.style.display = show ? 'block' : 'none';
    }
  }

  /**
   * Exibe mensagem de erro
   */
  showError(message) {
    if (this.error) {
      this.error.textContent = message;
      this.error.style.display = 'block';
      setTimeout(() => {
        this.error.style.display = 'none';
      }, 5000);
    }
  }

  /**
   * Exibe mensagem de sucesso
   */
  showSuccess(message) {
    if (this.success) {
      this.success.textContent = message;
      this.success.style.display = 'block';
      setTimeout(() => {
        this.success.style.display = 'none';
      }, 3000);
    }
  }

  /**
   * Exibe tela de login
   */
  showLoginScreen() {
    if (this.loginScreen) this.loginScreen.style.display = 'block';
    if (this.authSection) this.authSection.style.display = 'none';
    if (this.mainContent) this.mainContent.style.display = 'none';
  }

  /**
   * Exibe estado autenticado
   */
  async showAuthenticatedState() {
    if (this.loginScreen) this.loginScreen.style.display = 'none';
    if (this.authSection) this.authSection.style.display = 'block';
    if (this.mainContent) this.mainContent.style.display = 'block';

    // Atualiza informações do usuário
    try {
      const user = await this.authController.getCurrentUser();
      const permissions = await this.authController.getUserPermissions();

      if (user) {
        const userEmail = user.email || 'Usuário';
        const userProfile = permissions?.profile || 'User';

        if (this.userEmail) {
          this.userEmail.textContent = userEmail;
        }
        if (this.userProfile) {
          this.userProfile.textContent = `Perfil: ${userProfile}`;
        }
        if (this.userProfileHeader) {
          this.userProfileHeader.textContent = `${userEmail} (${userProfile})`;
          this.userProfileHeader.style.display = 'block';
        }

        // Mostra dropdown de ambiente apenas para Admin
        if (this.environmentSection && userProfile === 'Admin') {
          this.environmentSection.style.display = 'block';
        } else if (this.environmentSection) {
          this.environmentSection.style.display = 'none';
        }
      }

      // Carrega produtos agendados
      await this.loadScheduledProducts();
    } catch (error) {
      console.error('Erro ao carregar informações do usuário:', error);
    }
  }

  /**
   * Manipula login
   */
  async handleLogin() {
    const email = this.email?.value?.trim();
    const password = this.password?.value?.trim();

    if (!email || !password) {
      this.showError('Preencha todos os campos');
      return;
    }

    if (this.loginSubmitBtn) {
      this.loginSubmitBtn.disabled = true;
      this.loginSubmitBtn.textContent = 'Entrando...';
    }

    try {
      // Login sempre inicia no ambiente de produção
      const result = await this.authController.login(email, password, 'prod');

      if (result.success) {
        this.showSuccess('Login realizado com sucesso!');

        // Limpa o formulário
        if (this.email) this.email.value = '';
        if (this.password) this.password.value = '';
      } else {
        this.showError(result.message || 'Erro ao fazer login');
      }
    } catch (error) {
      console.error('Erro no login:', error);
      this.showError('Erro ao conectar com o servidor');
    } finally {
      if (this.loginSubmitBtn) {
        this.loginSubmitBtn.disabled = false;
        this.loginSubmitBtn.textContent = 'Entrar';
      }
    }
  }

  /**
   * Manipula mudança de ambiente
   */
  async handleEnvironmentChange() {
    const newEnvironment = this.serverUrl?.value;
    if (!newEnvironment) {
      return;
    }

    const isAuthenticated = await this.authController.isAuthenticated();
    if (!isAuthenticated) {
      return;
    }

    try {
      // Reconecta com o novo ambiente
      await this.authController.switchEnvironment(newEnvironment);
      this.showSuccess(`Ambiente alterado para ${newEnvironment === 'prod' ? 'Produção' : 'Desenvolvimento'}`);

      // Recarrega produtos agendados do novo ambiente
      await this.loadScheduledProducts();
    } catch (error) {
      console.error('Erro ao alterar ambiente:', error);
      this.showError('Erro ao alterar ambiente');
    }
  }

  /**
   * Carrega produtos agendados
   */
  async loadScheduledProducts() {
    try {
      const isAuthenticated = await this.authController.isAuthenticated();
      if (!isAuthenticated) {
        return;
      }

      // Busca produtos através do controller
      const products = await this.productController.getScheduledProducts();
      const count = products.length;

      this.updateProductsDisplay(products, count);

      // Inicia monitoramento em tempo real
      await this.productController.startRealtimeMonitoring();
    } catch (error) {
      console.error('Erro ao carregar produtos agendados:', error);
      if (this.pendingCount) {
        this.pendingCount.textContent = '0';
      }
    }
  }

  /**
   * Atualiza display dos produtos
   */
  updateProductsDisplay(products, count) {
    // Atualiza contador
    if (this.pendingCount) {
      this.pendingCount.textContent = count || '0';
    }

    // Atualiza display da fila
    this.updateQueueDisplay(products);
  }

  /**
   * Limpa display dos produtos
   */
  clearProductsDisplay() {
    if (this.pendingCount) {
      this.pendingCount.textContent = '-';
    }
    this.updateQueueDisplay([]);
  }

  /**
   * Atualiza display da fila
   */
  updateQueueDisplay(products) {
    if (!this.queueList || !this.queueEmpty) {
      return;
    }

    // Limpa lista atual
    this.queueList.innerHTML = '';

    if (!products || products.length === 0) {
      this.queueList.appendChild(this.queueEmpty);
      return;
    }

    // Cria itens da fila
    products.slice(0, 10).forEach((product, index) => {
      const queueItem = document.createElement('div');
      queueItem.className = 'queue-item';

      const scheduledTime = new Date(product.date_schedule);
      const timeString = scheduledTime.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });

      queueItem.innerHTML = `
        <div class="queue-number">${index + 1}</div>
        <div class="queue-content">
          <div class="queue-title" title="${product.title || product.nome_produto}">${product.title || product.nome_produto}</div>
          <div class="queue-time">${timeString}</div>
        </div>
      `;

      this.queueList.appendChild(queueItem);
    });
  }

  /**
   * Manipula logout
   */
  async handleLogout() {
    try {
      await this.authController.logout();
      this.showSuccess('Logout realizado com sucesso!');

      // Limpa informações do header
      if (this.userProfileHeader) {
        this.userProfileHeader.style.display = 'none';
      }

      // Limpa fila de agendamentos
      this.scheduledProducts = [];
      this.clearProductsDisplay();
    } catch (error) {
      console.error('Erro no logout:', error);
      this.showError('Erro ao fazer logout');
    }
  }

  /**
   * Cleanup da view
   */
  cleanup() {
    if (this.appController) {
      this.appController.cleanup();
    }
  }
}