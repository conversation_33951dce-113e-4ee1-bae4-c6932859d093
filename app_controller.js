/**
 * Controlador principal da aplicação
 */
class AppController {
  constructor(config = null) {
    this.config = config;
    this.authController = null;
    this.productController = null;
    this.authUseCase = null;
    this.productUseCase = null;
    this.isInitialized = false;
    
    console.log('AppController criado');
  }

  /**
   * Inicializa o controlador
   */
  async initialize() {
    try {
      console.log('🚀 Inicializando AppController...');

      // Inicializa configurações se necessário
      if (!this.config && typeof config !== 'undefined') {
        await config.initialize();
        this.config = config;
      }

      // Inicializa casos de uso
      await this.initializeUseCases();

      // Inicializa controladores
      await this.initializeControllers();

      // Configura listeners de autenticação
      this.setupAuthListeners();

      // Verifica status inicial de autenticação
      const isAuthenticated = await this.authController.isAuthenticated();
      console.log(`Status de autenticação inicial: ${isAuthenticated}`);

      this.isInitialized = true;
      console.log('✅ AppController inicializado com sucesso');

      return true;
    } catch (error) {
      console.error('❌ Erro ao inicializar AppController:', error);
      throw error;
    }
  }

  /**
   * Inicializa os casos de uso
   */
  async initializeUseCases() {
    try {
      // Verifica se as classes estão disponíveis
      if (typeof AuthUseCase !== 'undefined') {
        this.authUseCase = new AuthUseCase();
      } else {
        console.warn('AuthUseCase não disponível');
      }

      if (typeof ProductUseCase !== 'undefined') {
        this.productUseCase = new ProductUseCase();
      } else {
        console.warn('ProductUseCase não disponível');
      }

      console.log('✅ Casos de uso inicializados');
    } catch (error) {
      console.error('❌ Erro ao inicializar casos de uso:', error);
      throw error;
    }
  }

  /**
   * Inicializa os controladores
   */
  async initializeControllers() {
    try {
      // Inicializa AuthController
      if (typeof AuthController !== 'undefined' && this.authUseCase) {
        this.authController = new AuthController(this.authUseCase);
        await this.authController.initialize();
      } else {
        // Fallback para AuthService básico
        if (typeof AuthService !== 'undefined') {
          // Verifica se estamos em Service Worker ou DOM
          const isServiceWorker = typeof window === 'undefined';
          const authService = isServiceWorker ? 
            (typeof authServiceInstance !== 'undefined' ? authServiceInstance : new AuthService()) :
            (typeof window !== 'undefined' && window.authService ? window.authService : new AuthService());
          
          this.authController = {
            isAuthenticated: async () => {
              return authService.isAuthenticated;
            },
            login: async (email, password, env = 'prod') => {
              return await authService.login(email, password, env);
            },
            logout: async () => {
              return await authService.logout();
            },
            getCurrentUser: async () => {
              return authService.user;
            },
            getUserPermissions: async () => {
              return authService.permissions;
            },
            addAuthStateListener: (callback) => {
              // Implementação básica
              console.log('AuthStateListener adicionado (fallback)');
            }
          };
        }
      }

      // Inicializa ProductController
      if (typeof ProductController !== 'undefined' && this.productUseCase) {
        this.productController = new ProductController(this.productUseCase);
        console.log('✅ ProductController inicializado com ProductUseCase');
      } else {
        // Fallback para ProductsPage básico
        if (typeof ProductsPage !== 'undefined') {
          // Verifica se estamos em Service Worker ou DOM
          const isServiceWorker = typeof window === 'undefined';
          
          this.productController = {
            getProducts: async () => {
              if (isServiceWorker) {
                // Em Service Worker, retorna array vazio ou usa instância global se disponível
                return [];
              } else {
                const productsPage = (typeof window !== 'undefined' && window.ProductsPage) || new ProductsPage();
                return productsPage.getAllProducts();
              }
            },
            scheduleProduct: async (product, time) => {
              if (isServiceWorker) {
                // Em Service Worker, retorna erro ou implementa lógica alternativa
                return { success: false, error: 'ProductController não disponível em Service Worker' };
              } else {
                const productsPage = (typeof window !== 'undefined' && window.ProductsPage) || new ProductsPage();
                return await productsPage.scheduleProduct(product, time);
              }
            },
            getScheduledProducts: async () => {
              if (isServiceWorker) {
                // Em Service Worker, retorna array vazio ou usa instância global se disponível
                return [];
              } else {
                const productsPage = (typeof window !== 'undefined' && window.ProductsPage) || new ProductsPage();
                return productsPage.getOrderedProducts();
              }
            }
          };
          console.log('✅ ProductController inicializado com fallback ProductsPage');
        } else {
          // Fallback mínimo se nada estiver disponível
          this.productController = {
            getProducts: async () => [],
            scheduleProduct: async () => ({ success: false, error: 'ProductController não disponível' }),
            getScheduledProducts: async () => []
          };
          console.warn('⚠️ ProductController inicializado com fallback mínimo');
        }
      }

      console.log('✅ Controladores inicializados');
    } catch (error) {
      console.error('❌ Erro ao inicializar controladores:', error);
      throw error;
    }
  }

  /**
   * Configura listeners de autenticação
   */
  setupAuthListeners() {
    if (this.authController && this.authController.addAuthStateListener) {
      this.authController.addAuthStateListener((authState) => {
        console.log('Estado de autenticação alterado:', authState);
        
        // Dispara evento customizado
        const event = new CustomEvent('authStateChanged', {
          detail: authState
        });
        document.dispatchEvent(event);
      });
    }
  }

  /**
   * Obtém o controlador de autenticação
   * @returns {AuthController}
   */
  getAuthController() {
    return this.authController;
  }

  /**
   * Obtém o controlador de produtos
   * @returns {ProductController}
   */
  getProductController() {
    if (!this.productController) {
      console.warn('⚠️ ProductController não inicializado, retornando fallback');
      return {
        getProducts: async () => [],
        scheduleProduct: async () => ({ success: false, error: 'ProductController não inicializado' }),
        getScheduledProducts: async () => []
      };
    }
    return this.productController;
  }

  /**
   * Verifica se está inicializado
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * Obtém informações de debug
   */
  getDebugInfo() {
    return {
      initialized: this.isInitialized,
      config: !!this.config,
      auth: !!this.authController,
      product: !!this.productController,
      authUseCase: !!this.authUseCase,
      productUseCase: !!this.productUseCase
    };
  }

  /**
   * Limpa recursos
   */
  cleanup() {
    try {
      if (this.authController && this.authController.cleanup) {
        this.authController.cleanup();
      }

      if (this.productController && this.productController.cleanup) {
        this.productController.cleanup();
      }

      this.authController = null;
      this.productController = null;
      this.authUseCase = null;
      this.productUseCase = null;
      this.isInitialized = false;

      console.log('✅ AppController limpo');
    } catch (error) {
      console.error('❌ Erro ao limpar AppController:', error);
    }
  }

  /**
   * Método estático para criar instância
   * @param {Object} config - Configuração opcional
   * @returns {AppController}
   */
  static create(config = null) {
    return new AppController(config);
  }
}

// Exporta AppController de forma compatível com Service Worker e DOM
if (typeof window !== 'undefined') {
  window.AppController = AppController;
} else if (typeof self !== 'undefined') {
  // Em Service Worker, usa self em vez de window
  self.AppController = AppController;
}