<!DOCTYPE html>
<html lang="pt-BR">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Promobell WhatsApp Automation</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      width: 100%;
      min-height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #333;
      overflow-x: hidden;
    }

    .header {
      background: rgba(255, 255, 255, 0.1);
      padding: 12px 20px;
      text-align: center;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .logo {
      width: 32px;
      height: 32px;
      margin: 0 auto 8px;
      background: #25D366;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: white;
    }

    .title {
      color: white;
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 3px;
    }

    .subtitle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 11px;
    }

    .content {
      padding: 15px;
      background: white;
    }

    .status-card {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 15px;
      border-left: 4px solid #28a745;
    }

    .status-card.inactive {
      border-left-color: #dc3545;
    }

    .status-title {
      font-weight: 600;
      font-size: 14px;
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #28a745;
    }

    .status-indicator.inactive {
      background: #dc3545;
    }

    .status-text {
      font-size: 12px;
      color: #666;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-label {
      display: block;
      font-size: 13px;
      font-weight: 500;
      margin-bottom: 8px;
      color: #333;
    }

    .form-input {
      width: 100%;
      padding: 8px 10px;
      border: 2px solid #e9ecef;
      border-radius: 6px;
      font-size: 13px;
      transition: border-color 0.2s;
    }

    .form-input:focus {
      outline: none;
      border-color: #667eea;
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked+.slider {
      background-color: #25D366;
    }

    input:checked+.slider:before {
      transform: translateX(26px);
    }

    .btn {
      width: 100%;
      padding: 10px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 8px;
    }

    .btn-primary {
      background: #667eea;
      color: white;
    }

    .btn-primary:hover {
      background: #5a6fd8;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #5a6268;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .hidden {
      display: none !important;
    }

    .user-info {
      background: #e8f5e8;
      border: 1px solid #c3e6cb;
      border-radius: 6px;
      padding: 10px;
      margin-bottom: 10px;
      font-size: 12px;
    }

    .user-email {
      font-weight: 600;
      color: #155724;
    }

    .user-profile {
      color: #666;
      margin-top: 2px;
    }

    .logout-btn {
      background: #dc3545;
      color: white;
      border: none;
      padding: 6px 12px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      margin-top: 8px;
    }

    .logout-btn:hover {
      background: #c82333;
    }

    .stats {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      margin-bottom: 15px;
    }

    .stat-card {
      background: #f8f9fa;
      padding: 10px;
      border-radius: 6px;
      text-align: center;
    }

    .stat-number {
      font-size: 16px;
      font-weight: 600;
      color: #667eea;
    }

    .stat-label {
      font-size: 10px;
      color: #666;
      margin-top: 2px;
    }

    .loading {
      display: none;
      text-align: center;
      padding: 20px;
      color: #666;
    }

    .error {
      background: #f8d7da;
      color: #721c24;
      padding: 10px;
      border-radius: 6px;
      font-size: 12px;
      margin-bottom: 15px;
      display: none;
    }

    .queue-item {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 6px;
      padding: 10px;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .queue-number {
      background: #667eea;
      color: white;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      flex-shrink: 0;
    }

    .queue-content {
      flex: 1;
      min-width: 0;
    }

    .queue-title {
      font-size: 12px;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-bottom: 2px;
    }

    .queue-time {
      font-size: 10px;
      color: #666;
    }

    .success {
      background: #d4edda;
      color: #155724;
      padding: 10px;
      border-radius: 6px;
      font-size: 12px;
      margin-bottom: 15px;
      display: none;
    }
  </style>
</head>

<body>
  <div class="header">
        <div class="logo">📱</div>
        <div class="title">Promobell Channel Extension</div>
        <div class="subtitle" id="userProfileHeader" style="display: none;"></div>
      </div>

  <div class="content">
    <div class="loading" id="loading">
      Carregando configurações...
    </div>

    <div class="error" id="error"></div>
    <div class="success" id="success"></div>

    <!-- Tela de Login (mostrada quando não autenticado) -->
    <div id="loginScreen" style="display: none;">
      <div class="form-group">
        <label class="form-label" for="email">Email</label>
        <input type="email" id="email" class="form-input" placeholder="Digite seu email" required>
      </div>

      <div class="form-group">
        <label class="form-label" for="password">Senha</label>
        <input type="password" id="password" class="form-input" placeholder="Digite sua senha" required>
      </div>

      <button class="btn btn-primary" id="loginSubmitBtn" style="width: 100%; margin-bottom: 10px;">
        Entrar
      </button>
    </div>

    <!-- Seção de Autenticação (mostrada quando autenticado) -->
    <div id="authSection" class="status-card" style="display: none;">
      <div class="status-title">
        <div id="authIndicator" class="status-indicator"></div>
        <span id="authStatus">Autenticado</span>
      </div>
      <div id="authText" class="status-text">Conectado ao servidor</div>
      
      <!-- Dropdown de Ambiente (apenas para Admin) -->
      <div id="environmentSection" class="form-group" style="margin-top: 15px; display: none;">
        <label class="form-label" for="serverUrl">Ambiente de Execução</label>
        <select id="serverUrl" class="form-input">
          <option value="prod">Produção</option>
          <option value="dev">Desenvolvimento</option>
        </select>
      </div>
      
      <div id="userInfo" class="user-info" style="margin-top: 10px;">
        <div class="user-email" id="userEmail"></div>
        <div class="user-profile" id="userProfile"></div>
        <button class="logout-btn" id="logoutBtn">Sair</button>
      </div>
    </div>

    <div id="mainContent" style="display: none;">
      <!-- Seção de Status da Automação (visível apenas quando autenticado) -->
      <div id="automationSection" class="status-card">
        <div class="status-title">
          <span class="status-indicator" id="statusIndicator"></span>
          <span id="statusTitle">Automação Ativa</span>
        </div>
        <div class="status-text" id="statusText">
          Monitorando produtos agendados
        </div>
      </div>

      <!-- Configuração do Canal -->
      <div class="form-group">
        <label class="form-label" for="channelName">Nome ou ID do Canal</label>
        <input type="text" id="channelName" class="form-input"
          placeholder="Ex: Promobell {Dev} ou 0029VbApX2gBadmdU9AB2H3V">
        <small>Você pode usar o nome do canal ou colar o link/ID do canal</small>
      </div>

      <!-- Toggle da Automação -->
      <div class="form-group">
        <label class="form-label">
          Automação
          <label class="toggle-switch" style="float: right;">
            <input type="checkbox" id="automationToggle">
            <span class="slider"></span>
          </label>
        </label>
      </div>

      <!-- Estatísticas -->
      <div class="stats">
        <div class="stat-card">
          <div class="stat-number" id="pendingCount">-</div>
          <div class="stat-label">Pendentes</div>
        </div>
        <div class="stat-card">
          <div class="stat-number" id="sentCount">-</div>
          <div class="stat-label">Enviados</div>
        </div>
      </div>

      <!-- Botões de Ação -->
      <button class="btn btn-success" id="saveConfigBtn">
        Salvar Configurações
      </button>

      <!-- Card de Fila de Agendamentos -->
      <div id="queueCard" class="status-card" style="margin-top: 15px;">
        <div class="status-title">
          <span>📋 Fila de agendamentos</span>
        </div>
        <div id="queueList" style="max-height: 200px; overflow-y: auto; margin-top: 10px;">
          <div id="queueEmpty" style="text-align: center; color: #666; font-size: 12px; padding: 20px;">
            Nenhum produto agendado
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="supabase.js"></script>
  <script src="env-loader.js"></script>
  <script src="config.js"></script>
  <script src="auth.js"></script>
  <script src="app_controller.js"></script>
  <script src="storage.js"></script>
  <script src="utils.js"></script>
  <script src="products_page.js"></script>
  <script src="products.js"></script>
  <script src="popup.js"></script>
  <script src="sidebar_page.js"></script>
  <script src="sidebar.js"></script>
</body>
</html>